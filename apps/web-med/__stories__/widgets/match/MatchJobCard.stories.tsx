import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { MatchContextValue } from "@/widgets/match/context/MatchContext";

import { MatchJobCard } from "@/widgets/match";
import { MatchContext } from "@/widgets/match/context/MatchContext";

// Create a story wrapper that provides mock data to the real MatchProvider
function StoryMatchProvider({
  children,
  mockData,
  loading = false,
  error = null,
}: {
  children: React.ReactNode;
  mockData?: MatchContextValue;
  loading?: boolean;
  error?: Error | null;
}) {
  const contextValue: MatchContextValue = mockData || {
    match: null,
    loading: false,
    error: null,
    acceptRate: async () => {},
    counterRate: async () => {},
    submitOffer: async () => {},
    sendMessage: async () => {},
    currentRate: null,
    isNegotiating: false,
    canAcceptRate: false,
    canCounterRate: false,
    providerName: "",
    providerAvatar: null,
    providerSpecialty: "",
    jobTitle: "",
    jobRole: "",
    jobPaymentType: "",
    jobSalary: null,
    organizationName: "",
    organizationAvatar: null,
  };

  return (
    <MatchContext.Provider
      value={{
        ...contextValue,
        loading,
        error,
      }}
    >
      {children}
    </MatchContext.Provider>
  );
}

const meta: Meta<typeof MatchJobCard> = {
  title: "Widgets/Match/MatchJobCard",
  component: MatchJobCard,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story, { args }) => (
      <div className="w-80">
        <Story {...args} />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof MatchJobCard>;

// Base mock context data
const mockMatchData: MatchContextValue = {
  // Data
  match: {
    id: "match-123",
    status: "NEGOTIATING",
    initiator: "PROVIDER",
    initiationNote:
      "I'm interested in this position and would like to discuss the rate.",
    createdAt: new Date("2024-01-15T10:00:00Z"),
    updatedAt: new Date("2024-01-15T14:30:00Z"),
    jobId: "job-456",
    providerId: "provider-789",
    organizationId: "org-101",
    applicationId: null,
    offerId: null,
    positionId: null,
    contractId: null,
    threadId: null,
    initiatedAt: new Date("2024-01-15T10:00:00Z"),
    initiatedBy: "provider-789",
    job: {
      id: "job-456",
      status: "PUBLISHED",
      summary: "Emergency Department Physician",
      role: "Emergency Medicine",
      paymentType: "HOURLY",
      paymentAmount: 150,
      allowRateNegotiation: true,
      minNegotiableRate: 120,
      maxNegotiableRate: 200,
    },
    provider: {
      id: "provider-789",
      title: "Emergency Medicine Physician",
      person: {
        id: "person-111",
        firstName: "Dr. Sarah",
        lastName: "Johnson",
        avatar: null,
      },
    },
    organization: {
      id: "org-101",
      name: "City General Hospital",
      avatar: null,
    },
    compensation: {
      id: "comp-222",
      minRate: 120,
      maxRate: 200,
      currentOfferRate: 165,
      finalAgreedRate: null,
      rateStrategy: "balanced",
      negotiationCount: 2,
      negotiationStatus: "ACTIVE",
      lastOfferBy: "org-101",
      offerExpiresAt: null,
      paymentType: "HOURLY",
    },
    application: undefined,
    offer: undefined,
    position: undefined,
    contract: undefined,
    thread: undefined,
    steps: undefined,
  },
  loading: false,
  error: null,

  // Actions
  acceptRate: async () => {
    console.log("Accept rate");
  },
  counterRate: async () => {
    console.log("Counter rate");
  },
  submitOffer: async () => {
    console.log("Submit offer");
  },
  sendMessage: async () => {
    console.log("Send message");
  },

  // Computed values
  currentRate: 165,
  isNegotiating: true,
  canAcceptRate: true,
  canCounterRate: true,

  // Provider data
  providerName: "Dr. Sarah Johnson",
  providerAvatar: null,
  providerSpecialty: "Emergency Medicine Physician",

  // Job data
  jobTitle: "Emergency Department Physician",
  jobRole: "Emergency Medicine",
  jobPaymentType: "HOURLY",
  jobSalary: "$150",

  // Organization data
  organizationName: "City General Hospital",
  organizationAvatar: null,
};

export const Default: Story = {
  args: {
    onViewDetails: () => alert("View job details clicked"),
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={false} error={null} mockData={mockMatchData}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

export const Loading: Story = {
  args: {
    onViewDetails: () => alert("View job details clicked"),
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={true}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

export const Errored: Story = {
  args: {
    onViewDetails: () => alert("View job details clicked"),
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider error={new Error("Failed to load job data")}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

export const SurgicalRole: Story = {
  args: {
    onViewDetails: () => alert("View job details clicked"),
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        mockData={{
          ...mockMatchData,
          jobTitle: "Orthopedic Surgeon",
          jobRole: "Orthopedic Surgery",
          jobPaymentType: "SALARY",
          jobSalary: "$450,000",
          match: {
            ...mockMatchData.match!,
            job: {
              ...mockMatchData.match!.job!,
              summary: "Orthopedic Surgeon",
              role: "Orthopedic Surgery",
              paymentType: "SALARY" as const,
              paymentAmount: 450000,
            },
          },
        }}
      >
        <Story />
      </StoryMatchProvider>
    ),
  ],
};
