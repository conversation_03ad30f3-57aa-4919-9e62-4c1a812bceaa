"use client";

import Link from "next/link";

import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import AppView from "@axa/ui/layouts/AppView";
import { Badge } from "@axa/ui/primitives/badge";
import { <PERSON><PERSON> } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Separator } from "@axa/ui/primitives/separator";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import TimeAgo from "@axa/ui/shared/TimeAgo";

import type { RouterError, RouterOutputs } from "@/api";

import { MatchInitiator, MatchStatus } from "@/api";
import ProviderRole from "@/components/common/ProviderRole";
import { ErrorFallback } from "@/components/shared/Error";

const i18n = {
  en: {
    title: "Match Details",
    description: "View and manage this job match",
    sections: {
      overview: "Match Overview",
      job: "Job Details",
      organization: "Organization",
      compensation: "Compensation",
      actions: "Actions",
    },
    labels: {
      status: "Status",
      initiator: "Initiated By",
      created: "Created",
      jobTitle: "Job Title",
      role: "Role",
      location: "Location",
      organization: "Organization",
      finalRate: "Final Agreed Rate",
      currentOffer: "Current Offer",
      rateRange: "Rate Range",
      noRate: "Rate not specified",
    },
    actions: {
      accept: "Accept Match",
      decline: "Decline Match",
      withdraw: "Withdraw Application",
      negotiate: "Negotiate Rate",
      viewJob: "View Full Job Details",
      viewOrganization: "View Organization Profile",
    },
    status: {
      [MatchStatus.PENDING]: "Pending",
      [MatchStatus.ACCEPTED]: "Accepted",
      [MatchStatus.DECLINED]: "Declined",
      [MatchStatus.VALIDATING]: "Validating",
      [MatchStatus.NEGOTIATING]: "Negotiating",
      [MatchStatus.FINALIZING]: "Finalizing",
      [MatchStatus.MATCHED]: "Matched",
      [MatchStatus.WITHDRAWN]: "Withdrawn",
      [MatchStatus.CANCELLED]: "Cancelled",
      [MatchStatus.EXPIRED]: "Expired",
    },
    initiator: {
      [MatchInitiator.PROVIDER]: "You applied",
      [MatchInitiator.ORGANIZATION]: "Organization invited you",
      [MatchInitiator.MUTUAL]: "Mutual interest",
      [MatchInitiator.REFERRAL]: "Referral",
    },
  },
  links: {
    jobs: "/app/jobs/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export interface ProviderMatchProps {
  loading?: boolean;
  match: {
    data?: RouterOutputs["jobs"]["matches"]["get"];
    error?: RouterError;
    loading?: boolean;
  };
  onAccept?: (matchId: string) => void;
  onDecline?: (matchId: string) => void;
  onWithdraw?: (matchId: string) => void;
  onNegotiate?: (matchId: string) => void;
}

export function ProviderMatchSkeleton() {
  return <Skeleton className="size-full" />;
}

function MatchStatusBadge({ status }: { status: MatchStatus }) {
  const statusColors = {
    [MatchStatus.PENDING]: "bg-yellow-100 text-yellow-800",
    [MatchStatus.ACCEPTED]: "bg-green-100 text-green-800",
    [MatchStatus.DECLINED]: "bg-red-100 text-red-800",
    [MatchStatus.VALIDATING]: "bg-blue-100 text-blue-800",
    [MatchStatus.NEGOTIATING]: "bg-orange-100 text-orange-800",
    [MatchStatus.FINALIZING]: "bg-purple-100 text-purple-800",
    [MatchStatus.MATCHED]: "bg-emerald-100 text-emerald-800",
    [MatchStatus.WITHDRAWN]: "bg-gray-100 text-gray-800",
    [MatchStatus.CANCELLED]: "bg-red-100 text-red-800",
    [MatchStatus.EXPIRED]: "bg-gray-100 text-gray-800",
  };

  return (
    <Badge className={`${statusColors[status] || "bg-gray-100 text-gray-800"}`}>
      {i18n.en.status[status] || status}
    </Badge>
  );
}

export default function ProviderMatch({
  loading,
  match,
  onAccept,
  onDecline,
  onWithdraw,
  onNegotiate,
}: ProviderMatchProps) {
  if (match.error) {
    return <ErrorFallback error={match.error} />;
  }

  const matchData = match.data;
  const isLoading = loading || match.loading;

  if (isLoading || !matchData) {
    return <ProviderMatchSkeleton />;
  }

  const canAccept =
    matchData.status === MatchStatus.PENDING &&
    matchData.initiator === MatchInitiator.ORGANIZATION;
  const canDecline = matchData.status === MatchStatus.PENDING;
  const canWithdraw =
    matchData.status === MatchStatus.PENDING &&
    matchData.initiator === MatchInitiator.PROVIDER;
  const canNegotiate = [MatchStatus.PENDING, MatchStatus.NEGOTIATING].includes(
    matchData.status,
  );

  return (
    <AppView
      loading={isLoading}
      title={i18n.en.title}
      description={i18n.en.description}
    >
      <div className="flex h-full flex-col gap-6">
        {/* Match Overview */}
        <Card>
          <CardHeader>
            <CardTitle>{i18n.en.sections.overview}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {i18n.en.labels.status}
                </label>
                <div className="mt-1">
                  <MatchStatusBadge status={matchData.status} />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {i18n.en.labels.initiator}
                </label>
                <p className="mt-1 text-sm">
                  {i18n.en.initiator[matchData.initiator] ||
                    matchData.initiator}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {i18n.en.labels.created}
                </label>
                <p className="mt-1 text-sm">
                  <TimeAgo date={matchData.createdAt} />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Job Details */}
          {matchData.job && (
            <Card>
              <CardHeader>
                <CardTitle>{i18n.en.sections.job}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {i18n.en.labels.jobTitle}
                  </label>
                  <p className="mt-1 font-medium">
                    {matchData.job.summary || "Untitled Job"}
                  </p>
                </div>
                {matchData.job.role && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {i18n.en.labels.role}
                    </label>
                    <div className="mt-1">
                      <ProviderRole roleName={matchData.job.role} />
                    </div>
                  </div>
                )}
                {matchData.job.location && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {i18n.en.labels.location}
                    </label>
                    <p className="mt-1 text-sm">
                      {matchData.job.location.name}
                    </p>
                  </div>
                )}
                <Separator />
                <Button asChild variant="outline" className="w-full">
                  <Link
                    href={i18n.links.jobs.replace("[id]", matchData.job.id)}
                  >
                    {i18n.en.actions.viewJob}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Organization Details */}
          {matchData.organization && (
            <Card>
              <CardHeader>
                <CardTitle>{i18n.en.sections.organization}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {i18n.en.labels.organization}
                  </label>
                  <div className="mt-1">
                    <PreviewOrganization
                      organization={matchData.organization}
                      loading={isLoading}
                    />
                  </div>
                </div>
                <Separator />
                <Button asChild variant="outline" className="w-full">
                  <Link
                    href={i18n.links.organizations.replace(
                      "[id]",
                      matchData.organization.id,
                    )}
                  >
                    {i18n.en.actions.viewOrganization}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Compensation */}
        {matchData.compensation && (
          <Card>
            <CardHeader>
              <CardTitle>{i18n.en.sections.compensation}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {matchData.compensation.finalAgreedRate && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {i18n.en.labels.finalRate}
                    </label>
                    <p className="mt-1 text-lg font-semibold text-green-600">
                      ${matchData.compensation.finalAgreedRate}/hr
                    </p>
                  </div>
                )}
                {matchData.compensation.currentOfferRate && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {i18n.en.labels.currentOffer}
                    </label>
                    <p className="mt-1 text-lg font-semibold text-orange-600">
                      ${matchData.compensation.currentOfferRate}/hr
                    </p>
                  </div>
                )}
                {matchData.compensation.minRate &&
                  matchData.compensation.maxRate && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {i18n.en.labels.rateRange}
                      </label>
                      <p className="mt-1 text-lg font-semibold">
                        ${matchData.compensation.minRate}-$
                        {matchData.compensation.maxRate}/hr
                      </p>
                    </div>
                  )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>{i18n.en.sections.actions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {canAccept && (
                <Button
                  onClick={() => onAccept?.(matchData.id)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {i18n.en.actions.accept}
                </Button>
              )}
              {canDecline && (
                <Button
                  onClick={() => onDecline?.(matchData.id)}
                  variant="destructive"
                >
                  {i18n.en.actions.decline}
                </Button>
              )}
              {canWithdraw && (
                <Button
                  onClick={() => onWithdraw?.(matchData.id)}
                  variant="outline"
                  className="border-orange-600 text-orange-600 hover:bg-orange-50"
                >
                  {i18n.en.actions.withdraw}
                </Button>
              )}
              {canNegotiate && (
                <Button
                  onClick={() => onNegotiate?.(matchData.id)}
                  variant="outline"
                  className="border-blue-600 text-blue-600 hover:bg-blue-50"
                >
                  {i18n.en.actions.negotiate}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppView>
  );
}
