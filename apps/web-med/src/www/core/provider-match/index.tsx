"use client";

import { Suspense } from "react";
import { toast } from "@axa/ui/primitives/toast";

import { api } from "@/api";

import ProviderMatch, { ProviderMatchSkeleton } from "./ProviderMatch";

export interface ProviderMatchViewProps {
  loading?: boolean;
  id: string;
}

export function ProviderMatchView(props: ProviderMatchViewProps) {
  // Fetch match data
  const match = api.providers.matches.get.useQuery(
    {
      id: props.id,
      include: {
        job: true,
        organization: true,
        compensation: true,
      },
    },
    {
      enabled: !!props.id,
    },
  );

  // Match action mutations
  const acceptMutation = api.providers.matches.accept.useMutation({
    onSuccess: async () => {
      await match.refetch();
      toast.success("Match accepted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to accept match: ${error.message}`);
    },
  });

  const declineMutation = api.providers.matches.decline.useMutation({
    onSuccess: async () => {
      await match.refetch();
      toast.success("Match declined");
    },
    onError: (error) => {
      toast.error(`Failed to decline match: ${error.message}`);
    },
  });

  const withdrawMutation = api.providers.matches.withdraw.useMutation({
    onSuccess: async () => {
      await match.refetch();
      toast.success("Application withdrawn");
    },
    onError: (error) => {
      toast.error(`Failed to withdraw application: ${error.message}`);
    },
  });

  const negotiateMutation = api.providers.matches.negotiate.useMutation({
    onSuccess: async () => {
      await match.refetch();
      toast.success("Rate negotiation initiated");
    },
    onError: (error) => {
      toast.error(`Failed to initiate negotiation: ${error.message}`);
    },
  });

  const handleAccept = async (matchId: string) => {
    await acceptMutation.mutateAsync({ id: matchId });
  };

  const handleDecline = async (matchId: string) => {
    await declineMutation.mutateAsync({ id: matchId });
  };

  const handleWithdraw = async (matchId: string) => {
    await withdrawMutation.mutateAsync({ id: matchId });
  };

  const handleNegotiate = async (matchId: string) => {
    // For now, just mark as negotiating. In the future, this could open a negotiation dialog
    await negotiateMutation.mutateAsync({ 
      id: matchId,
      proposedRate: 0, // This would come from a form/dialog
      message: "Rate negotiation requested",
    });
  };

  return (
    <ProviderMatch
      loading={props.loading}
      match={{
        data: match.data,
        loading: match.isLoading,
        error: match.error,
      }}
      onAccept={handleAccept}
      onDecline={handleDecline}
      onWithdraw={handleWithdraw}
      onNegotiate={handleNegotiate}
    />
  );
}

export default function ProviderMatchPage({ id }: { id: string }) {
  return (
    <Suspense fallback={<ProviderMatchSkeleton />}>
      <ProviderMatchView id={id} />
    </Suspense>
  );
}
