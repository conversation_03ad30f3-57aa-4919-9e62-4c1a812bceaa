"use client";

import Link from "next/link";

import AppView from "@axa/ui/layouts/AppView";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import TimeAgo from "@axa/ui/shared/TimeAgo";

import type { RouterError, RouterOutputs } from "@/api";

import { MatchStatus } from "@/api";
import { ErrorFallback } from "@/components/shared/Error";

const i18n = {
  en: {
    title: "Match Details",
    description: "View and manage match details",
    backToMatches: "Back to Matches",
    matchDetails: "Match Details",
    jobDetails: "Job Details",
    providerDetails: "Provider Details",
    organizationDetails: "Organization Details",
    rateDetails: "Rate Details",
    status: "Status",
    initiator: "Initiated By",
    createdAt: "Created",
    updatedAt: "Last Updated",
    noData: "No data available",
    viewJob: "View Job",
    viewProvider: "View Provider",
    viewOrganization: "View Organization",
    currentRate: "Current Rate",
    agreedRate: "Agreed Rate",
    rateRange: "Rate Range",
    noRate: "No rate set",
  },
  links: {
    matches: "/app/matches",
    jobs: "/app/jobs/[id]",
    providers: "/app/providers/[id]",
    organizations: "/app/organizations/[id]",
  },
  status: {
    [MatchStatus.PENDING]: "Pending",
    [MatchStatus.ACCEPTED]: "Accepted",
    [MatchStatus.DECLINED]: "Declined",
    [MatchStatus.VALIDATING]: "Validating",
    [MatchStatus.NEGOTIATING]: "Negotiating",
    [MatchStatus.FINALIZING]: "Finalizing",
    [MatchStatus.MATCHED]: "Matched",
    [MatchStatus.WITHDRAWN]: "Withdrawn",
    [MatchStatus.CANCELLED]: "Cancelled",
    [MatchStatus.EXPIRED]: "Expired",
  },
};

export interface MatchDetailProps {
  loading?: boolean;
  match?: {
    loading?: boolean;
    error?: RouterError;
    data?: RouterOutputs["jobs"]["matches"]["get"];
    refresh: () => Promise<void>;
  };
  matchId: string;
}

export function MatchDetailSkeleton() {
  return (
    <AppView loading title={i18n.en.title} description={i18n.en.description}>
      <div className="space-y-6">
        <Skeleton className="h-32 w-full" />
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
        </div>
      </div>
    </AppView>
  );
}

function StatusBadge({ status }: { status: MatchStatus }) {
  const statusColors = {
    [MatchStatus.PENDING]: "bg-yellow-100 text-yellow-800",
    [MatchStatus.ACCEPTED]: "bg-green-100 text-green-800",
    [MatchStatus.DECLINED]: "bg-red-100 text-red-800",
    [MatchStatus.VALIDATING]: "bg-blue-100 text-blue-800",
    [MatchStatus.NEGOTIATING]: "bg-orange-100 text-orange-800",
    [MatchStatus.FINALIZING]: "bg-purple-100 text-purple-800",
    [MatchStatus.MATCHED]: "bg-emerald-100 text-emerald-800",
    [MatchStatus.WITHDRAWN]: "bg-gray-100 text-gray-800",
    [MatchStatus.CANCELLED]: "bg-red-100 text-red-800",
    [MatchStatus.EXPIRED]: "bg-gray-100 text-gray-800",
  };

  return (
    <Badge
      className={`${statusColors[status] || "bg-gray-100 text-gray-800"}`}
      variant="secondary"
    >
      {i18n.en.status[status] || status}
    </Badge>
  );
}

export default function MatchDetail({
  loading,
  match,
  matchId,
}: MatchDetailProps) {
  if (match?.error) {
    return (
      <AppView title={i18n.en.title} description={i18n.en.description}>
        <ErrorFallback error={match.error} />
      </AppView>
    );
  }

  if (loading || match?.loading || !match?.data) {
    return <MatchDetailSkeleton />;
  }

  const matchData = match.data;
  const job = matchData.job;
  const provider = matchData.provider;
  const organization = matchData.organization;
  const compensation = matchData.compensation;

  return (
    <AppView title={i18n.en.title} description={i18n.en.description}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button variant="ghost" asChild>
            <Link href={i18n.links.matches}>← {i18n.en.backToMatches}</Link>
          </Button>
        </div>

        {/* Match Overview */}
        <Card>
          <CardHeader>
            <CardTitle>{i18n.en.matchDetails}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {i18n.en.status}
                </label>
                <div className="mt-1">
                  <StatusBadge status={matchData.status} />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {i18n.en.initiator}
                </label>
                <p className="mt-1 text-sm">{matchData.initiator}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {i18n.en.createdAt}
                </label>
                <p className="mt-1 text-sm">
                  <TimeAgo date={matchData.createdAt} />
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Details Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Job Details */}
          {job && (
            <Card>
              <CardHeader>
                <CardTitle>{i18n.en.jobDetails}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-medium">
                    {job.summary || "Untitled Job"}
                  </h4>
                  {job.role && (
                    <p className="text-sm text-muted-foreground">{job.role}</p>
                  )}
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href={i18n.links.jobs.replace("[id]", job.id)}>
                    {i18n.en.viewJob}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Provider Details */}
          {provider && (
            <Card>
              <CardHeader>
                <CardTitle>{i18n.en.providerDetails}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  {provider.person && (
                    <h4 className="font-medium">
                      {provider.person.firstName} {provider.person.lastName}
                    </h4>
                  )}
                  {provider.title && (
                    <p className="text-sm text-muted-foreground">
                      {provider.title}
                    </p>
                  )}
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link
                    href={i18n.links.providers.replace("[id]", provider.id)}
                  >
                    {i18n.en.viewProvider}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Organization Details */}
          {organization && (
            <Card>
              <CardHeader>
                <CardTitle>{i18n.en.organizationDetails}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-medium">{organization.name}</h4>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link
                    href={i18n.links.organizations.replace(
                      "[id]",
                      organization.id,
                    )}
                  >
                    {i18n.en.viewOrganization}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Rate Details */}
          <Card>
            <CardHeader>
              <CardTitle>{i18n.en.rateDetails}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {compensation ? (
                <div className="space-y-2">
                  {compensation.finalAgreedRate && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {i18n.en.agreedRate}
                      </label>
                      <p className="text-lg font-semibold text-green-600">
                        ${compensation.finalAgreedRate}/hr
                      </p>
                    </div>
                  )}
                  {compensation.currentOfferRate &&
                    !compensation.finalAgreedRate && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          {i18n.en.currentRate}
                        </label>
                        <p className="text-lg font-semibold text-orange-600">
                          ${compensation.currentOfferRate}/hr
                        </p>
                      </div>
                    )}
                  {compensation.minRate && compensation.maxRate && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {i18n.en.rateRange}
                      </label>
                      <p className="text-sm text-muted-foreground">
                        ${compensation.minRate} - ${compensation.maxRate}/hr
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  {i18n.en.noRate}
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AppView>
  );
}
