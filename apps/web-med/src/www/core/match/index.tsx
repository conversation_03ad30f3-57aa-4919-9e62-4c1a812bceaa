"use client";

import { Suspense, use, useCallback } from "react";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

import MatchDetail, { MatchDetailSkeleton } from "./Match";

export interface MatchViewProps {
  loading?: boolean;
  id: string;
  match?: Promise<RouterOutputs["jobs"]["matches"]["get"]>;
}

export function MatchView({ loading, match, id }: MatchViewProps) {
  const matchQuery = api.jobs.matches.get.useQuery(
    {
      id,
      include: {
        job: true,
        provider: true,
        organization: true,
        compensation: true,
        position: true,
        contract: true,
        thread: true,
        steps: true,
      },
    },
    {
      initialData: match ? use(match) : undefined,
    },
  );

  return (
    <MatchDetail
      loading={loading ?? matchQuery.isLoading}
      match={{
        data: matchQuery.data,
        loading: matchQuery.isLoading,
        error: matchQuery.error,
        refresh: useCallback(async () => {
          await matchQuery.refetch();
        }, [matchQuery.refetch]),
      }}
      matchId={id}
    />
  );
}

export default function MatchPage(props: MatchViewProps) {
  return (
    <Suspense fallback={<MatchDetailSkeleton />}>
      <MatchView match={props.match} loading={props.loading} id={props.id} />
    </Suspense>
  );
}
