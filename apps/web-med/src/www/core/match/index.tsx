"use client";

import { Suspense, use, useCallback } from "react";

import { toast } from "@axa/ui/primitives/toast";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { useUser } from "@/components/contexts/User";

import MatchDetail, { MatchDetailSkeleton } from "./Match";

export interface MatchViewProps {
  loading?: boolean;
  id: string;
  match?: Promise<RouterOutputs["jobs"]["matches"]["get"]>;
  mode?: "organization" | "provider";
}

export function MatchView({ loading, match, id, mode }: MatchViewProps) {
  const user = useUser();
  const actualMode =
    mode || (user.mode === "PROVIDER" ? "provider" : "organization");

  const matchQuery = api.jobs.matches.get.useQuery(
    {
      id,
      include: {
        job: true,
        provider: true,
        organization: true,
        compensation: true,
        position: true,
        contract: true,
        thread: true,
        steps: true,
      },
    },
    {
      initialData: match ? use(match) : undefined,
    },
  );

  // Action mutations using the new unified actions API
  const acceptMutation = api.jobs.matches.actions.accept.useMutation({
    onSuccess: async () => {
      await matchQuery.refetch();
      toast.success("Match accepted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to accept match: ${error.message}`);
    },
  });

  const declineMutation = api.jobs.matches.actions.decline.useMutation({
    onSuccess: async () => {
      await matchQuery.refetch();
      toast.success("Match declined");
    },
    onError: (error) => {
      toast.error(`Failed to decline match: ${error.message}`);
    },
  });

  const withdrawMutation = api.jobs.matches.actions.withdraw.useMutation({
    onSuccess: async () => {
      await matchQuery.refetch();
      toast.success("Match withdrawn");
    },
    onError: (error) => {
      toast.error(`Failed to withdraw match: ${error.message}`);
    },
  });

  const handleAccept = async (matchId: string) => {
    await acceptMutation.mutateAsync({ id: matchId });
  };

  const handleDecline = async (matchId: string) => {
    await declineMutation.mutateAsync({ id: matchId });
  };

  const handleWithdraw = async (matchId: string) => {
    await withdrawMutation.mutateAsync({ id: matchId });
  };

  return (
    <MatchDetail
      loading={loading ?? matchQuery.isLoading}
      match={{
        data: matchQuery.data,
        loading: matchQuery.isLoading,
        error: matchQuery.error,
        refresh: useCallback(async () => {
          await matchQuery.refetch();
        }, [matchQuery.refetch]),
      }}
      matchId={id}
      mode={actualMode}
      onAccept={handleAccept}
      onDecline={handleDecline}
      onWithdraw={handleWithdraw}
    />
  );
}

export default function MatchPage(props: MatchViewProps) {
  return (
    <Suspense fallback={<MatchDetailSkeleton />}>
      <MatchView match={props.match} loading={props.loading} id={props.id} />
    </Suspense>
  );
}
