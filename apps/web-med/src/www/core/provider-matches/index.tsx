"use client";

import { Suspense, use } from "react";

import { SearchParams } from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { useListProviderMatches } from "@/hooks/lists/use-list-provider-matches";

import ProviderMatches, { ProviderMatchesSkeleton } from "./ProviderMatches";

export interface ProviderMatchesViewProps {
  loading?: boolean;
  matches?: Promise<RouterOutputs["providers"]["matches"]["list"]>;
}

export function ProviderMatchesView(props: ProviderMatchesViewProps) {
  const {
    data,
    loading: isLoading,
    error,
  } = useListProviderMatches({
    group: "provider-match",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });

  const loading = props.loading ?? isLoading;

  return (
    <ProviderMatches
      loading={loading}
      matches={{
        data: data
          ? {
              items: data.items,
              total: data.total,
            }
          : undefined,
        loading: isLoading,
        error: error,
      }}
    />
  );
}

export default function ProviderMatchesPage({ matches }: ProviderMatchesViewProps) {
  return (
    <Suspense fallback={<ProviderMatchesSkeleton />}>
      <SearchParams>
        <ProviderMatchesView matches={matches} />
      </SearchParams>
    </Suspense>
  );
}
