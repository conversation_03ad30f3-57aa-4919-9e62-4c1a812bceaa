"use client";

import AppView from "@axa/ui/layouts/AppView";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type { RouterError, RouterOutputs } from "@/api";

import { ErrorFallback } from "@/components/shared/Error";
import ListMatches from "@/components/tables/ListMatches";

const i18n = {
  en: {
    title: "My Matches",
    description:
      "View and manage your job matches. Track applications you've submitted and invitations you've received from organizations.",
  },
};

export interface ProviderMatchesProps {
  loading?: boolean;
  matches: {
    data?: RouterOutputs["jobs"]["matches"]["list"];
    error?: RouterError;
    loading?: boolean;
  };
}

export function ProviderMatchesSkeleton() {
  return <Skeleton className="size-full" />;
}

export default function ProviderMatches(props: ProviderMatchesProps) {
  return (
    <AppView
      loading={props.loading}
      title={i18n.en.title}
      description={i18n.en.description}
    >
      <div className="flex h-full flex-col gap-6">
        {props.matches.error && <ErrorFallback error={props.matches.error} />}

        <ListMatches
          matches={props.matches.data}
          loading={props.loading ?? props.matches.loading}
          mode="provider"
        />
      </div>
    </AppView>
  );
}
