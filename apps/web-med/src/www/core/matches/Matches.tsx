"use client";

import AppView from "@axa/ui/layouts/AppView";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type { RouterError, RouterOutputs } from "@/api";

import { useUser } from "@/components/contexts/User";
import { ErrorFallback } from "@/components/shared/Error";
import ListMatches from "@/components/tables/ListMatches";

const i18n = {
  en: {
    organization: {
      title: "Matches",
      description:
        "Matches represent connections between providers and job opportunities. Track the status of negotiations, rate agreements, and match progress.",
    },
    provider: {
      title: "My Matches",
      description:
        "View and manage your job matches. Track applications you've submitted and invitations you've received from organizations.",
    },
  },
};

export interface MatchesProps {
  loading?: boolean;
  matches: {
    data?: RouterOutputs["jobs"]["matches"]["list"];
    error?: RouterError;
    loading?: boolean;
  };
  mode?: "organization" | "provider";
}

export function MatchesSkeleton() {
  return <Skeleton className="size-full" />;
}

export default function Matches(props: MatchesProps) {
  const user = useUser();
  const mode =
    props.mode || (user.mode === "PROVIDER" ? "provider" : "organization");
  const content = i18n.en[mode];

  return (
    <AppView
      loading={props.loading}
      title={content.title}
      description={content.description}
    >
      <div className="flex h-full flex-col gap-6">
        {props.matches.error && <ErrorFallback error={props.matches.error} />}

        <ListMatches
          matches={props.matches.data}
          loading={props.loading ?? props.matches.loading}
          mode={mode}
        />
      </div>
    </AppView>
  );
}
