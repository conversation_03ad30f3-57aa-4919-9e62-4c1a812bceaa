"use client";

import AppView from "@axa/ui/layouts/AppView";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type { RouterError, RouterOutputs } from "@/api";

import { ErrorFallback } from "@/components/shared/Error";
import ListMatches from "@/components/tables/ListMatches";

const i18n = {
  en: {
    title: "Matches",
    description:
      "Matches represent connections between providers and job opportunities. Track the status of negotiations, rate agreements, and match progress.",
  },
};

export interface MatchesProps {
  loading?: boolean;
  matches: {
    data?: RouterOutputs["jobs"]["matches"]["list"];
    error?: RouterError;
    loading?: boolean;
  };
}

export function MatchesSkeleton() {
  return <Skeleton className="size-full" />;
}

export default function Matches(props: MatchesProps) {
  return (
    <AppView
      loading={props.loading}
      title={i18n.en.title}
      description={i18n.en.description}
    >
      <div className="flex h-full flex-col gap-6">
        {props.matches.error && <ErrorFallback error={props.matches.error} />}

        <ListMatches
          matches={props.matches.data}
          loading={props.loading ?? props.matches.loading}
        />
      </div>
    </AppView>
  );
}
