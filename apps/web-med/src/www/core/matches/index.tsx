"use client";

import { Suspense } from "react";

import { SearchParams } from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { useUser } from "@/components/contexts/User";
import { useListMatches } from "@/hooks/lists/use-list-matches";

import Matches, { MatchesSkeleton } from "./Matches";

export interface MatchesViewProps {
  loading?: boolean;
  matches?: Promise<RouterOutputs["jobs"]["matches"]["list"]>;
  mode?: "organization" | "provider";
}

export function MatchesView(props: MatchesViewProps) {
  const user = useUser();
  const mode =
    props.mode || (user.mode === "PROVIDER" ? "provider" : "organization");

  // Use unified hook with appropriate filtering based on mode
  const {
    data,
    loading: isLoading,
    error,
  } = useListMatches({
    group: mode === "provider" ? "provider-match" : "match",
    defaultPageSize: 10,
    defaultPageIndex: 0,
    // Filter by current user's provider ID when in provider mode
    providerId: mode === "provider" ? user.providerId || undefined : undefined,
    // Filter by current user's organization ID when in organization mode
    organizationId:
      mode === "organization" ? user.organizationId || undefined : undefined,
  });

  const loading = props.loading ?? isLoading;

  return (
    <Matches
      loading={loading}
      mode={mode}
      matches={{
        data: data
          ? {
              items: data.items as any, // Type compatibility between different match data structures
              total: data.total,
            }
          : undefined,
        loading: isLoading,
        error: error,
      }}
    />
  );
}

export default function MatchesPage({ matches }: MatchesViewProps) {
  return (
    <Suspense fallback={<MatchesSkeleton />}>
      <SearchParams>
        <MatchesView matches={matches} />
      </SearchParams>
    </Suspense>
  );
}
