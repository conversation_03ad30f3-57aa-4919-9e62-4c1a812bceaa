"use client";

import { Suspense, use } from "react";

import { SearchParams } from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { useListMatches } from "@/hooks/lists/use-list-matches";

import Matches, { MatchesSkeleton } from "./Matches";

export interface MatchesViewProps {
  loading?: boolean;
  matches?: Promise<RouterOutputs["matches"]["list"]>;
}

export function MatchesView(props: MatchesViewProps) {
  const {
    data,
    loading: isLoading,
    error,
  } = useListMatches({
    group: "match",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });

  const loading = props.loading ?? isLoading;

  return (
    <Matches
      loading={loading}
      matches={{
        data: data
          ? {
              items: data.items,
              total: data.total,
            }
          : undefined,
        loading: isLoading,
        error: error,
      }}
    />
  );
}

export default function MatchesPage({ matches }: MatchesViewProps) {
  return (
    <Suspense fallback={<MatchesSkeleton />}>
      <SearchParams>
        <MatchesView matches={matches} />
      </SearchParams>
    </Suspense>
  );
}
