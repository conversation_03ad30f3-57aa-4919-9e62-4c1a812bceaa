"use client";

import { motion } from "framer-motion";

import AppView from "@axa/ui/layouts/AppView";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type { RouterError, RouterOutputs } from "@/api";
import type { api } from "@/api/client";

import { JobPostMenu } from "@/components/actions/jobs/job";
import ActionLog from "@/components/shared/actions/ActionLog";
import { ErrorFallback } from "@/components/shared/Error";
import Chat from "@/components/widgets/messages";
import { ProspectingWidget } from "@/widgets/prospecting";
import JobFinances from "@/www/organizations/job/finances/JobFinances";
import JobAbstract from "@/www/organizations/job/JobAbstract";
import JobStatus from "@/www/organizations/job/JobStatus";
import JobSchedule from "@/www/organizations/job/schedule/JobSchedule";

import { JobFacility } from "./facility/JobFacility";
import JobPosition, { JobPositionSkeleton } from "./JobPosition";

const i18n = {
  en: {
    title: "Job",
  },
  links: {
    jobs: "/app/jobs",
  },
};

export function JobSkeleton() {
  return (
    <AppView>
      <div className="flex flex-col gap-6 md:gap-8">
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
        <JobPositionSkeleton />
        <Skeleton className="h-10 w-32" />
        <Skeleton className="h-10 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>
    </AppView>
  );
}

export interface JobProps {
  loading?: boolean;
  job: {
    loading: boolean;
    data?: RouterOutputs["jobs"]["get"];
    error?: RouterError;
    refetch: () => Promise<void>;
  };

  update?: ReturnType<typeof api.jobs.update.useMutation>;
  publish?: ReturnType<typeof api.jobs.organization.publish.useMutation>;
  unpublish?: ReturnType<typeof api.jobs.organization.unpublish.useMutation>;
  cancel?: ReturnType<typeof api.jobs.organization.cancel.useMutation>;
  linkDepartment?: ReturnType<typeof api.departments.link.useMutation>;
  unlinkDepartment?: ReturnType<typeof api.departments.unlink.useMutation>;
  linkLocation?: ReturnType<typeof api.locations.link.useMutation>;
  unlinkLocation?: ReturnType<typeof api.locations.unlink.useMutation>;
  linkContact?: ReturnType<typeof api.contacts.link.useMutation>;
  unlinkContact?: ReturnType<typeof api.contacts.unlink.useMutation>;
  createContact?: ReturnType<typeof api.contacts.create.useMutation>;
  updateContact?: ReturnType<typeof api.contacts.update.useMutation>;
  deleteContact?: ReturnType<typeof api.contacts.delete.useMutation>;
  createSchedule?: ReturnType<typeof api.schedule.create.useMutation>;
  updateSchedule?: ReturnType<typeof api.schedule.update.useMutation>;
  createBlock?: ReturnType<typeof api.schedule.blocks.create.useMutation>;
  updateBlock?: ReturnType<typeof api.schedule.blocks.update.useMutation>;
  deleteBlock?: ReturnType<typeof api.schedule.blocks.delete.useMutation>;
  sendOffer?: ReturnType<typeof api.offers.organization.send.useMutation>;
  acceptApplication?: ReturnType<
    typeof api.applications.organization.approve.useMutation
  >;
  rejectApplication?: ReturnType<
    typeof api.applications.organization.reject.useMutation
  >;
  withdrawOffer?: ReturnType<
    typeof api.offers.organization.withdraw.useMutation
  >;
}

export default function Job(props: JobProps) {
  return (
    <AppView
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      loading={props.loading || props.job.loading}
      goBackUrl={i18n.links.jobs}
    >
      <div className="flex justify-end">
        <JobPostMenu
          // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
          loading={props.loading || props.job.loading}
          job={props.job.data}
        />
      </div>

      <div className="flex flex-col gap-8 lg:gap-10">
        {props.job.error && <ErrorFallback error={props.job.error} />}

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <JobStatus
            loading={props.job.loading}
            job={props.job.data}
            publish={props.publish}
            unpublish={props.unpublish}
            cancel={props.cancel}
          />
        </motion.div>

        {props.job.data?.position && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <JobPosition
              loading={props.loading}
              job={props.job.data}
              refresh={props.job.refetch}
            />
          </motion.div>
        )}

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 xl:grid-cols-3 xl:gap-10">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="col-span-1 grid h-full grid-rows-1 gap-8 md:grid-rows-2 xl:col-span-2 xl:gap-10"
          >
            <JobAbstract
              // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
              loading={props.loading || props.job.loading}
              job={props.job.data}
            />
            <JobFacility
              // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
              loading={props.loading || props.job.loading}
              job={props.job.data}
              linkLocation={props.linkLocation}
              unlinkLocation={props.unlinkLocation}
              linkDepartment={props.linkDepartment}
              unlinkDepartment={props.unlinkDepartment}
              createContact={props.createContact}
              updateContact={props.updateContact}
              deleteContact={props.deleteContact}
              linkContact={props.linkContact}
              unlinkContact={props.unlinkContact}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <JobFinances
              // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
              loading={props.loading || props.job.loading}
              job={props.job.data}
              update={props.update}
            />
          </motion.div>
        </div>

        <div className="grid grid-cols-1 gap-6 md:gap-8 xl:grid-cols-2">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="size-full"
          >
            <JobSchedule
              loading={props.loading}
              job={props.job.data}
              createSchedule={props.createSchedule}
              updateSchedule={props.updateSchedule}
              createBlock={props.createBlock}
              updateBlock={props.updateBlock}
              deleteBlock={props.deleteBlock}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="size-full"
          >
            <ProspectingWidget
              jobId={props.job.data?.id ?? ""}
              onViewJobDetails={() => {
                // Job details are already visible on this page
              }}
              onMatchCreated={(match) => {
                // Optionally handle match creation
                console.log("Match created:", match);
              }}
            />
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8"
        >
          <ActionLog
            loading={props.loading}
            actions={props.job.data?.actions ?? []}
          />
          <Chat
            threadId={props.job.data?.thread?.id}
            resourceType="job"
            resourceId={props.job.data?.id}
            onThreadCreate={async () => {
              await props.job.refetch();
            }}
          />
        </motion.div>
      </div>
    </AppView>
  );
}
