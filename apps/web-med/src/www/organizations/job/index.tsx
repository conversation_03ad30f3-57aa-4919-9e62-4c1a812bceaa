"use client";

import { Suspense, use } from "react";

import { toast } from "@axa/ui/primitives/toast";
import { SearchParams } from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { JobPostStatus } from "@/api";
import { api } from "@/api/client";
import Job from "@/www/organizations/job/Job";

const PAGE_SIZE = 5;

export function JobView(props: {
  loading?: boolean;
  id: string;
  job?: Promise<RouterOutputs["jobs"]["get"]>;
}) {
  const job = api.jobs.get.useQuery(
    {
      id: props.id,
      include: {
        organization: true,
        location: true,
        department: true,
        specialties: true,
        schedule: true,
        thread: true,
        offers: true,
        applications: true,
        actions: true,
        contracts: true,
        provider: true,
        contacts: true,
        position: true,
      },
    },
    {
      enabled: !props.loading,
      initialData: props.job ? use(props.job) : undefined,
    },
  );

  const update = api.jobs.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const publish = api.jobs.organization.publish.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post published successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const unpublish = api.jobs.organization.unpublish.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post unpublished successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const cancel = api.jobs.organization.cancel.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post cancelled successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const linkDepartment = api.departments.link.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post linked to department successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const unlinkDepartment = api.departments.unlink.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post unlinked from department successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const linkLocation = api.locations.link.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post linked to facility successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const unlinkLocation = api.locations.unlink.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post unlinked from facility successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const linkContact = api.contacts.link.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact linked to job successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const unlinkContact = api.contacts.unlink.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact unlinked from job successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const createContact = api.contacts.create.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact created successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const updateContact = api.contacts.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const deleteContact = api.contacts.delete.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact deleted successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const createSchedule = api.schedule.create.useMutation({
    onSuccess: () => {
      void job.refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const updateSchedule = api.schedule.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Schedule updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const createBlock = api.schedule.blocks.create.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Time block created successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const updateBlock = api.schedule.blocks.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Time block updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const deleteBlock = api.schedule.blocks.delete.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Time block deleted successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  return (
    <Job
      loading={props.loading}
      job={{
        loading: job.isLoading,
        data: job.data,
        error: job.error,
        refetch: async () => {
          await job.refetch();
        },
      }}
      update={update}
      publish={publish}
      unpublish={unpublish}
      cancel={cancel}
      linkDepartment={linkDepartment}
      unlinkDepartment={unlinkDepartment}
      linkLocation={linkLocation}
      unlinkLocation={unlinkLocation}
      linkContact={linkContact}
      unlinkContact={unlinkContact}
      createContact={createContact}
      updateContact={updateContact}
      deleteContact={deleteContact}
      createSchedule={createSchedule}
      updateSchedule={updateSchedule}
      createBlock={createBlock}
      updateBlock={updateBlock}
      deleteBlock={deleteBlock}
    />
  );
}

export default function JobPage(props: {
  id: string;
  job?: Promise<RouterOutputs["jobs"]["get"]>;
}) {
  return (
    <Suspense fallback={<JobView loading id={props.id} />}>
      <SearchParams>
        <JobView id={props.id} job={props.job} />
      </SearchParams>
    </Suspense>
  );
}
