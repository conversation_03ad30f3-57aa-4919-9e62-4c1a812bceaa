"use client";

import { cn } from "@axa/ui/lib/utils";

import {
  ProspectJobOverview,
  ProspectProviderSearch,
  ProspectList,
} from "./components";
import { ProspectingProvider } from "./context/ProspectingContext";

export interface ProspectingWidgetProps {
  jobId: string;
  onViewJobDetails?: () => void;
  onMatchCreated?: (match: any) => void;
  className?: string;
  layout?: "default" | "compact" | "sidebar";
}

function DefaultLayout({
  onViewJobDetails,
  className,
}: {
  onViewJobDetails?: () => void;
  className?: string;
}) {
  return (
    <div className={cn("grid grid-cols-12 gap-6 h-full", className)}>
      {/* Left Sidebar - Job Overview and Search */}
      <div className="col-span-3 space-y-6">
        <ProspectJobOverview onViewDetails={onViewJobDetails} />
        <ProspectProviderSearch />
      </div>

      {/* Main Content - Provider List */}
      <div className="col-span-9">
        <ProspectList />
      </div>
    </div>
  );
}

function CompactLayout({
  onViewJobDetails,
  className,
}: {
  onViewJobDetails?: () => void;
  className?: string;
}) {
  return (
    <div className={cn("space-y-6", className)}>
      {/* Top Row - Job Overview and Search */}
      <div className="grid grid-cols-2 gap-6">
        <ProspectJobOverview onViewDetails={onViewJobDetails} />
        <ProspectProviderSearch />
      </div>

      {/* Bottom Row - Provider List */}
      <ProspectList />
    </div>
  );
}

function SidebarLayout({
  onViewJobDetails,
  className,
}: {
  onViewJobDetails?: () => void;
  className?: string;
}) {
  return (
    <div className={cn("grid grid-cols-4 gap-6 h-full", className)}>
      {/* Sidebar - Job Overview and Search */}
      <div className="col-span-1 space-y-6">
        <ProspectJobOverview onViewDetails={onViewJobDetails} showTitle={false} />
        <ProspectProviderSearch showTitle={false} />
      </div>

      {/* Main Content - Provider List */}
      <div className="col-span-3">
        <ProspectList showTitle={false} />
      </div>
    </div>
  );
}

export function ProspectingWidget({
  jobId,
  onViewJobDetails,
  onMatchCreated,
  className,
  layout = "default",
}: ProspectingWidgetProps) {
  const renderLayout = () => {
    switch (layout) {
      case "compact":
        return <CompactLayout onViewJobDetails={onViewJobDetails} className={className} />;
      case "sidebar":
        return <SidebarLayout onViewJobDetails={onViewJobDetails} className={className} />;
      default:
        return <DefaultLayout onViewJobDetails={onViewJobDetails} className={className} />;
    }
  };

  return (
    <ProspectingProvider jobId={jobId}>
      {renderLayout()}
    </ProspectingProvider>
  );
}
