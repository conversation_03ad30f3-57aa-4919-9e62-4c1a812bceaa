"use client";

import { <PERSON><PERSON><PERSON>ircle, RefreshCw } from "lucide-react";

import { Alert, AlertDescription } from "@axa/ui/primitives/alert";
import { Button } from "@axa/ui/primitives/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@axa/ui/primitives/card";

import { useProspecting } from "../context/ProspectingContext";
import { ProspectCard } from "./prospect-card";

const i18n = {
  en: {
    title: "Available Providers",
    noResults: "No providers found",
    noResultsDescription: "Try adjusting your search criteria or filters to find more providers.",
    error: "Failed to load providers",
    retry: "Retry",
    refresh: "Refresh",
    loading: "Searching for providers...",
  },
};

export interface ProspectListProps {
  className?: string;
  showTitle?: boolean;
  showRefresh?: boolean;
}

function ProspectListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <ProspectCard key={i} isLoading />
      ))}
    </div>
  );
}

function ProspectListEmpty() {
  return (
    <Card>
      <CardContent className="flex flex-col items-center justify-center py-12 text-center">
        <div className="rounded-full bg-muted p-3 mb-4">
          <AlertCircle className="size-6 text-muted-foreground" />
        </div>
        <h3 className="font-semibold text-lg mb-2">{i18n.en.noResults}</h3>
        <p className="text-muted-foreground text-sm max-w-md">
          {i18n.en.noResultsDescription}
        </p>
      </CardContent>
    </Card>
  );
}

function ProspectListError({ onRetry }: { onRetry: () => void }) {
  return (
    <Card>
      <CardContent className="p-4">
        <Alert variant="destructive">
          <AlertCircle className="size-4" />
          <AlertDescription className="flex items-center justify-between">
            {i18n.en.error}
            <Button variant="outline" size="sm" onClick={onRetry}>
              {i18n.en.retry}
            </Button>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}

export function ProspectList({
  className,
  showTitle = true,
  showRefresh = true,
}: ProspectListProps) {
  const {
    providers,
    totalProviders,
    loading,
    error,
    refreshProviders,
  } = useProspecting();

  const handleRefresh = async () => {
    await refreshProviders();
  };

  return (
    <div className={className}>
      {/* Header */}
      {showTitle && (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>
                {i18n.en.title}
                {!loading.providers && (
                  <span className="ml-2 text-sm font-normal text-muted-foreground">
                    ({totalProviders})
                  </span>
                )}
              </span>
              {showRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={loading.providers}
                >
                  <RefreshCw className={`size-4 mr-2 ${loading.providers ? "animate-spin" : ""}`} />
                  {i18n.en.refresh}
                </Button>
              )}
            </CardTitle>
          </CardHeader>
        </Card>
      )}

      {/* Content */}
      <div className="space-y-4">
        {/* Loading state */}
        {loading.providers && <ProspectListSkeleton />}

        {/* Error state */}
        {error.providers && !loading.providers && (
          <ProspectListError onRetry={handleRefresh} />
        )}

        {/* Empty state */}
        {!loading.providers && !error.providers && (!providers?.items || providers.items.length === 0) && (
          <ProspectListEmpty />
        )}

        {/* Provider list */}
        {!loading.providers && !error.providers && providers?.items && providers.items.length > 0 && (
          <>
            {providers.items.map((provider) => (
              <ProspectCard
                key={provider.id}
                provider={provider}
                onViewProfile={() => {
                  // Navigation handled by the Link in ProspectCard
                }}
              />
            ))}
          </>
        )}
      </div>
    </div>
  );
}
