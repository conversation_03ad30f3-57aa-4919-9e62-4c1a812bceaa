"use client";

import { SearchText } from "@axa/ui/search";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@axa/ui/primitives/card";

import { SearchSpecialty } from "@/components/selectors/SelectSpecialty";
import { useProspecting } from "../context/ProspectingContext";

const i18n = {
  en: {
    title: "Search Providers",
    search: "Search providers...",
    specialty: "Specialty",
    experience: "Experience",
    location: "Location",
    availability: "Availability",
    filters: "Filters",
    totalResults: "providers found",
  },
};

export interface ProspectProviderSearchProps {
  className?: string;
  showTitle?: boolean;
}

export function ProspectProviderSearch({
  className,
  showTitle = true,
}: ProspectProviderSearchProps) {
  const { totalProviders, loading } = useProspecting();

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {i18n.en.title}
            <span className="text-sm font-normal text-muted-foreground">
              {loading.providers ? "Searching..." : `${totalProviders} ${i18n.en.totalResults}`}
            </span>
          </CardTitle>
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        {/* Search input */}
        <SearchText
          name="prospect"
          group="prospecting"
          placeholder={i18n.en.search}
          className="w-full"
        />

        {/* Specialty filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            {i18n.en.specialty}
          </label>
          <SearchSpecialty
            name="specialty"
            group="prospecting"
            className="h-9 w-full"
          />
        </div>

        {/* Experience filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            {i18n.en.experience}
          </label>
          <select
            className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
            defaultValue=""
          >
            <option value="">Any experience</option>
            <option value="0-2">0-2 years</option>
            <option value="3-5">3-5 years</option>
            <option value="6-10">6-10 years</option>
            <option value="10+">10+ years</option>
          </select>
        </div>

        {/* Location filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            {i18n.en.location}
          </label>
          <input
            type="text"
            placeholder="City, State or ZIP"
            className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>

        {/* Availability filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            {i18n.en.availability}
          </label>
          <select
            className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
            defaultValue=""
          >
            <option value="">Any availability</option>
            <option value="immediate">Available immediately</option>
            <option value="week">Available within 1 week</option>
            <option value="month">Available within 1 month</option>
          </select>
        </div>
      </CardContent>
    </Card>
  );
}
