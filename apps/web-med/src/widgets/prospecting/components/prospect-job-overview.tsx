"use client";

import Link from "next/link";
import { MapPin, DollarSign, Clock, Building } from "lucide-react";

import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import { Card, CardContent, CardHeader, CardTitle } from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { useProspecting } from "../context/ProspectingContext";

const i18n = {
  en: {
    title: "Job Overview",
    viewDetails: "View Full Details",
    location: "Location",
    payment: "Payment",
    type: "Type",
    organization: "Organization",
    requirements: "Requirements",
    noLocation: "Location not specified",
    noPayment: "Payment not specified",
    noRequirements: "No specific requirements",
  },
};

export interface ProspectJobOverviewProps {
  className?: string;
  showTitle?: boolean;
  onViewDetails?: () => void;
}

function JobOverviewSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-5 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-28" />
        </div>
        <Skeleton className="h-9 w-full" />
      </CardContent>
    </Card>
  );
}

export function ProspectJobOverview({
  className,
  showTitle = true,
  onViewDetails,
}: ProspectJobOverviewProps) {
  const { job, jobTitle, jobRole, jobLocation, jobPaymentRange, loading } = useProspecting();

  if (loading.job || !job) {
    return <JobOverviewSkeleton />;
  }

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle>{i18n.en.title}</CardTitle>
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        {/* Job title and role */}
        <div className="space-y-2">
          <h3 className="font-semibold text-lg">{jobTitle || "Untitled Job"}</h3>
          {jobRole && (
            <Badge variant="secondary" className="text-sm">
              {jobRole}
            </Badge>
          )}
        </div>

        {/* Job details */}
        <div className="space-y-3">
          {/* Location */}
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="size-4 text-muted-foreground" />
            <span>
              {jobLocation || i18n.en.noLocation}
            </span>
          </div>

          {/* Payment */}
          <div className="flex items-center gap-2 text-sm">
            <DollarSign className="size-4 text-muted-foreground" />
            <span>
              {jobPaymentRange || i18n.en.noPayment}
            </span>
          </div>

          {/* Job type */}
          <div className="flex items-center gap-2 text-sm">
            <Clock className="size-4 text-muted-foreground" />
            <span>
              {job.paymentType === "HOURLY" ? "Hourly" : "Salary"}
            </span>
          </div>

          {/* Organization */}
          {job.organization && (
            <div className="flex items-center gap-2 text-sm">
              <Building className="size-4 text-muted-foreground" />
              <span>{job.organization.name}</span>
            </div>
          )}
        </div>

        {/* Requirements */}
        {job.requirements && job.requirements.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm text-muted-foreground">
              {i18n.en.requirements}
            </h4>
            <div className="flex flex-wrap gap-1">
              {job.requirements.slice(0, 3).map((req, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {req.name}
                </Badge>
              ))}
              {job.requirements.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{job.requirements.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* View details button */}
        <Button
          variant="outline"
          className="w-full"
          onClick={onViewDetails}
          asChild
        >
          <Link href={`/app/jobs/${job.id}`}>
            {i18n.en.viewDetails}
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
