"use client";

import Image from "next/image";
import Link from "next/link";
import { distance, point } from "@turf/turf";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Clock3,
  MapPin,
  StarIcon,
  UserCheck,
} from "lucide-react";

import { Alert, AlertDescription } from "@axa/ui/primitives/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import { Card, CardContent } from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type { RouterOutputs } from "@/api";

import { VerificationStatus } from "@/api";
import { useProspecting } from "../context/ProspectingContext";

const i18n = {
  en: {
    errors: {
      provider: "Failed to load provider data. Please try again.",
      createMatch: "Failed to create match. Please try again.",
    },
    verified: "Verified",
    pending: "Pending",
    miles: "miles",
    languages: "Languages:",
    viewProfile: "View Profile",
    createMatch: "Create Match",
    matchExists: "Match Exists",
    years: "years exp",
    availability: {
      availableNow: "Available Now",
      week: "1 week",
      twoWeeks: "2 weeks",
      month: "1 month",
      immediately: "Immediately",
    },
    matchStatus: {
      PENDING: "Match Pending",
      ACCEPTED: "Match Accepted",
      DECLINED: "Match Declined",
      NEGOTIATING: "Negotiating",
      MATCHED: "Matched",
      WITHDRAWN: "Withdrawn",
      CANCELLED: "Cancelled",
      EXPIRED: "Expired",
    },
  },
};

export type ProviderSearchStruct =
  RouterOutputs["providers"]["prospecting"]["search"]["items"][number];

export interface ProspectCardProps {
  provider?: ProviderSearchStruct;
  className?: string;
  isError?: boolean;
  isLoading?: boolean;
  onViewProfile?: () => void;
  onRetry?: () => void;
}

function ProspectCardSkeleton() {
  return (
    <Card>
      <CardContent className="flex items-center gap-4 p-4">
        <Skeleton className="size-12 shrink-0 rounded-full" />
        <div className="flex min-w-0 flex-1 flex-col gap-1">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-5 w-24" />
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-5 w-20" />
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-5 w-16" />
          </div>
        </div>
        <div className="flex shrink-0 gap-2">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-24" />
        </div>
      </CardContent>
    </Card>
  );
}

function ProspectCardError({ onRetry }: { onRetry?: () => void }) {
  return (
    <Card>
      <CardContent className="p-4">
        <Alert variant="destructive">
          <AlertCircle className="size-4" />
          <AlertDescription className="flex items-center justify-between">
            {i18n.en.errors.provider}
            {onRetry && (
              <Button variant="outline" size="sm" onClick={onRetry}>
                Retry
              </Button>
            )}
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}

function RatingStars({ rating }: { rating: number }) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating - fullStars >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex items-center">
      {[...Array(fullStars)].map((_, i) => (
        <StarIcon
          key={`full-${i}`}
          className="size-4 fill-yellow-400 text-yellow-400"
        />
      ))}
      {hasHalfStar && (
        <StarIcon className="size-4 fill-yellow-400 text-yellow-400" />
      )}
      {[...Array(emptyStars)].map((_, i) => (
        <StarIcon key={`empty-${i}`} className="size-4 text-yellow-400/40" />
      ))}
      <span className="ml-1 text-sm text-muted-foreground">
        ({rating.toFixed(1)})
      </span>
    </div>
  );
}

function computeDistance(
  provider?: ProviderSearchStruct,
  job?: RouterOutputs["jobs"]["get"],
): number {
  if (provider && job) {
    const jobAddress = job.location?.address;
    const providerAddress = provider.address;

    if (jobAddress && providerAddress) {
      const providerPoint = point([
        providerAddress.longitude,
        providerAddress.latitude,
      ]);
      const jobPoint = point([jobAddress.longitude, jobAddress.latitude]);

      return Math.round(
        distance(providerPoint, jobPoint, {
          units: "miles",
        }),
      );
    }
  }

  return 0;
}

export function ProspectCard({
  provider,
  className,
  isError,
  isLoading,
  onViewProfile,
  onRetry,
}: ProspectCardProps) {
  const {
    job,
    createMatch,
    hasExistingMatch,
    getMatchStatus,
    loading,
    error,
  } = useProspecting();

  if (isError) {
    return <ProspectCardError onRetry={onRetry} />;
  }

  if (!provider || isLoading) {
    return <ProspectCardSkeleton />;
  }

  // Calculate years of experience
  const totalExperience = provider.experiences?.reduce((total, exp) => {
    const start = new Date(exp.startDate);
    const end = exp.endDate ? new Date(exp.endDate) : new Date();
    return total + (end.getFullYear() - start.getFullYear());
  }, 0);

  // Get certifications from qualifications
  const certifications =
    provider.qualifications?.map((qual) => qual.name).slice(0, 2) || [];

  // Calculate distance
  const distanceValue = computeDistance(provider, job);

  const languages = provider.spokenLanguages;
  if (languages.length === 0) languages.push("English");

  const rating = provider.reviews?.average ?? 0;

  // Mock availability (would come from API in real implementation)
  const availability = i18n.en.availability.availableNow;

  // Determine verification status
  const isVerified =
    provider.verification?.status === VerificationStatus.APPROVED;
  const isPending =
    provider.verification?.status === VerificationStatus.PENDING;

  // Check if match exists and get status
  const matchExists = hasExistingMatch(provider.id);
  const matchStatus = getMatchStatus(provider.id);

  // Handle match creation
  const handleCreateMatch = async () => {
    if (matchExists) return;
    
    try {
      await createMatch(provider.id, `Interested in ${provider.person?.firstName} ${provider.person?.lastName} for this position.`);
    } catch (error) {
      console.error("Failed to create match:", error);
    }
  };

  return (
    <Card className={className}>
      <CardContent className="p-0">
        {/* Header with availability and verification badges */}
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center">
            <Badge className="flex items-center gap-1 rounded-full bg-green-500 px-3 py-1 text-white hover:bg-green-500">
              <Clock className="size-3.5" />
              {availability}
            </Badge>
          </div>

          {isVerified ? (
            <Badge
              variant="outline"
              className="bg-white font-medium text-blue-600"
            >
              <CheckCircle className="mr-1 size-3.5" />
              {i18n.en.verified}
            </Badge>
          ) : (
            isPending && (
              <Badge
                variant="outline"
                className="bg-white font-medium text-amber-500"
              >
                <Clock3 className="mr-1 size-3.5" />
                {i18n.en.pending}
              </Badge>
            )
          )}
        </div>

        <div className="p-4">
          {/* Main content */}
          <div className="flex items-start gap-4">
            {/* Avatar with verification badge */}
            <div className="relative">
              <Avatar className="size-16 shrink-0">
                <AvatarImage
                  asChild
                  src={provider.person?.avatar ?? ""}
                  alt={`${provider.person?.firstName} ${provider.person?.lastName}`}
                >
                  <Image
                    src={provider.person?.avatar ?? ""}
                    alt={`${provider.person?.firstName} ${provider.person?.lastName}`}
                    width={64}
                    height={64}
                  />
                </AvatarImage>
                <AvatarFallback>
                  {provider.person?.firstName[0]}
                  {provider.person?.lastName[0]}
                </AvatarFallback>
              </Avatar>

              {/* Small verification badge on avatar */}
              {isVerified && (
                <div className="absolute -bottom-1 -right-1 rounded-full bg-white p-0.5">
                  <CheckCircle className="size-4 fill-green-500 text-white" />
                </div>
              )}

              {/* Small pending badge on avatar */}
              {isPending && (
                <div className="absolute -bottom-1 -right-1 rounded-full bg-white p-0.5">
                  <Clock3 className="size-4 text-amber-500" />
                </div>
              )}
            </div>

            <div className="flex min-w-0 flex-1 flex-col gap-1">
              {/* Name */}
              <div className="flex items-center gap-2">
                <h3 className="text-xl font-semibold text-primary">
                  {provider.person?.firstName} {provider.person?.lastName}
                </h3>
              </div>

              {/* Title and experience */}
              <div className="text-sm text-muted-foreground">
                {provider.title && `${provider.title} • `}
                {totalExperience} {i18n.en.years}
              </div>

              {/* Languages */}
              <div className="text-sm">
                <span className="font-medium text-muted-foreground">
                  can speak
                </span>{" "}
                {languages.join(", ")}
              </div>

              {/* Rating */}
              <RatingStars rating={rating} />
            </div>

            {/* Distance */}
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <MapPin className="size-4" />
              <span className="font-semibold text-primary">
                {distanceValue}
              </span>{" "}
              {i18n.en.miles}
            </div>
          </div>

          {/* Certifications */}
          <div className="mt-4 flex flex-wrap gap-2">
            {certifications.map((cert, i) => (
              <Badge key={i} variant="outline" className="text-xs">
                {cert}
              </Badge>
            ))}
            {provider.title && (
              <Badge variant="outline" className="text-xs">
                {provider.title}
              </Badge>
            )}
          </div>

          {/* Match status if exists */}
          {matchExists && matchStatus && (
            <div className="mt-4">
              <Badge variant="secondary" className="flex w-fit items-center gap-1">
                <UserCheck className="size-3" />
                {i18n.en.matchStatus[matchStatus as keyof typeof i18n.en.matchStatus] || matchStatus}
              </Badge>
            </div>
          )}

          {/* Action buttons */}
          <div className="mt-4 grid grid-cols-2 gap-2">
            <Button variant="outline" onClick={onViewProfile} asChild>
              <Link href={`/app/providers/${provider.id}`}>
                {i18n.en.viewProfile}
              </Link>
            </Button>
            
            <Button
              className={matchExists ? "bg-gray-500 hover:bg-gray-600" : "bg-teal-500 hover:bg-teal-600"}
              onClick={handleCreateMatch}
              disabled={matchExists || loading.creating}
            >
              {loading.creating ? (
                "Creating..."
              ) : matchExists ? (
                i18n.en.matchExists
              ) : (
                i18n.en.createMatch
              )}
            </Button>
          </div>

          {/* Error message */}
          {error.creating && (
            <div className="mt-2">
              <Alert variant="destructive">
                <AlertCircle className="size-4" />
                <AlertDescription>
                  {i18n.en.errors.createMatch}
                </AlertDescription>
              </Alert>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
