"use client";

import { createContext, useCallback, useContext, useMemo } from "react";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { useSearchFilterValue, useSearchTextValue } from "@axa/ui/search";

// Types
export interface ProspectingContextValue {
  // Data
  job: RouterOutputs["jobs"]["get"] | null;
  providers: RouterOutputs["providers"]["prospecting"]["search"] | null;
  existingMatches: RouterOutputs["jobs"]["matches"]["list"] | null;
  loading: {
    job: boolean;
    providers: boolean;
    matches: boolean;
    creating: boolean;
  };
  error: {
    job: Error | null;
    providers: Error | null;
    matches: Error | null;
    creating: Error | null;
  };

  // Search parameters
  searchParams: {
    query: string | undefined;
    specialty: string | undefined;
    experience: string | undefined;
    location: string | undefined;
    availability: string | undefined;
  };

  // Actions
  createMatch: (providerId: string, message?: string) => Promise<RouterOutputs["jobs"]["matches"]["create"] | null>;
  refreshProviders: () => Promise<void>;
  refreshJob: () => Promise<void>;
  refreshMatches: () => Promise<void>;

  // Computed values
  jobTitle: string;
  jobRole: string;
  jobLocation: string;
  jobPaymentRange: string;
  totalProviders: number;
  hasExistingMatch: (providerId: string) => boolean;
  getMatchStatus: (providerId: string) => string | null;
}

const ProspectingContext = createContext<ProspectingContextValue | null>(null);

export interface ProspectingProviderProps {
  jobId: string;
  children: React.ReactNode;
}

export function ProspectingProvider({ jobId, children }: ProspectingProviderProps) {
  // Search parameters
  const query = useSearchTextValue("prospecting");
  const specialty = useSearchFilterValue<string>("specialty", "prospecting");
  const experience = useSearchFilterValue<string>("experience", "prospecting");
  const location = useSearchFilterValue<string>("location", "prospecting");
  const availability = useSearchFilterValue<string>("availability", "prospecting");

  // Fetch job data
  const {
    data: job,
    isLoading: jobLoading,
    error: jobError,
    refetch: refetchJob,
  } = api.jobs.get.useQuery(
    {
      id: jobId,
      include: {
        location: true,
        organization: true,
        requirements: true,
      },
    },
    { enabled: !!jobId },
  );

  // Fetch provider search results
  const {
    data: providers,
    isLoading: providersLoading,
    error: providersError,
    refetch: refetchProviders,
  } = api.providers.prospecting.search.useQuery(
    {
      query,
      specialty,
      experience,
      location,
      availability,
      jobId,
      pageSize: 20,
      pageNumber: 0,
    },
    { enabled: !!jobId },
  );

  // Fetch existing matches for this job
  const {
    data: existingMatches,
    isLoading: matchesLoading,
    error: matchesError,
    refetch: refetchMatches,
  } = api.jobs.matches.list.useQuery(
    {
      jobId,
      include: {
        provider: true,
        compensation: true,
      },
    },
    { enabled: !!jobId },
  );

  // Create match mutation
  const createMatchMutation = api.jobs.matches.create.useMutation();

  // Actions
  const createMatch = useCallback(
    async (providerId: string, message?: string) => {
      if (!job) return null;

      try {
        const match = await createMatchMutation.mutateAsync({
          jobId: job.id,
          providerId,
          initiator: "ORGANIZATION",
          initiationNote: message,
          includeCompensation: true,
        });

        // Refresh matches after creation
        await refetchMatches();

        return match;
      } catch (error) {
        console.error("Failed to create match:", error);
        return null;
      }
    },
    [job, createMatchMutation, refetchMatches],
  );

  const refreshProviders = useCallback(async () => {
    await refetchProviders();
  }, [refetchProviders]);

  const refreshJob = useCallback(async () => {
    await refetchJob();
  }, [refetchJob]);

  const refreshMatches = useCallback(async () => {
    await refetchMatches();
  }, [refetchMatches]);

  // Helper functions
  const hasExistingMatch = useCallback(
    (providerId: string) => {
      return existingMatches?.items.some((match) => match.providerId === providerId) ?? false;
    },
    [existingMatches],
  );

  const getMatchStatus = useCallback(
    (providerId: string) => {
      const match = existingMatches?.items.find((match) => match.providerId === providerId);
      return match?.status ?? null;
    },
    [existingMatches],
  );

  // Computed values
  const contextValue = useMemo((): ProspectingContextValue => {
    return {
      // Data
      job: job || null,
      providers: providers || null,
      existingMatches: existingMatches || null,
      loading: {
        job: jobLoading,
        providers: providersLoading,
        matches: matchesLoading,
        creating: createMatchMutation.isPending,
      },
      error: {
        job: jobError as Error | null,
        providers: providersError as Error | null,
        matches: matchesError as Error | null,
        creating: createMatchMutation.error as Error | null,
      },

      // Search parameters
      searchParams: {
        query,
        specialty,
        experience,
        location,
        availability,
      },

      // Actions
      createMatch,
      refreshProviders,
      refreshJob,
      refreshMatches,

      // Computed values
      jobTitle: job?.summary || "",
      jobRole: job?.role || "",
      jobLocation: job?.location?.address?.city || "",
      jobPaymentRange: job?.paymentAmount 
        ? `$${job.paymentAmount}${job.paymentType === "HOURLY" ? "/hr" : ""}`
        : "",
      totalProviders: providers?.total || 0,
      hasExistingMatch,
      getMatchStatus,
    };
  }, [
    job,
    providers,
    existingMatches,
    jobLoading,
    providersLoading,
    matchesLoading,
    createMatchMutation.isPending,
    jobError,
    providersError,
    matchesError,
    createMatchMutation.error,
    query,
    specialty,
    experience,
    location,
    availability,
    createMatch,
    refreshProviders,
    refreshJob,
    refreshMatches,
    hasExistingMatch,
    getMatchStatus,
  ]);

  return (
    <ProspectingContext.Provider value={contextValue}>
      {children}
    </ProspectingContext.Provider>
  );
}

export function useProspecting() {
  const context = useContext(ProspectingContext);
  if (!context) {
    throw new Error("useProspecting must be used within a ProspectingProvider");
  }
  return context;
}
