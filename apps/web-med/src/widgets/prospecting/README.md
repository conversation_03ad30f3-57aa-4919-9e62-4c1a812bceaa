# Prospecting Widget Suite

A composable widget suite for finding and matching providers to job opportunities using the new Matches API. Built with data/presentation separation using React Context and decomposed into individual kebab-case components.

## Architecture

### Data/Presentation Separation

- **`ProspectingContext`**: Centralized data management for provider search, job data, and match creation
- **Individual Components**: Pure presentation components that consume context
- **Composable Design**: Mix and match components for custom layouts

### Component Structure

```
prospecting/
├── context/
│   └── ProspectingContext.tsx      # Data layer with API integration
├── components/
│   ├── prospect-provider-search.tsx # Search filters and controls
│   ├── prospect-job-overview.tsx   # Job details display
│   ├── prospect-list.tsx           # Provider list container
│   ├── prospect-card.tsx           # Individual provider card
│   └── index.ts                    # Component exports
├── prospecting-center.tsx          # Main widget wrapper
└── index.ts                        # Main exports
```

## Components

### `ProspectingWidget`

Complete prospecting interface with job overview, search filters, and provider list with match creation.

```tsx
import { ProspectingWidget } from "@/widgets/prospecting";

<ProspectingWidget
  jobId="job-123"
  onViewJobDetails={() => navigate("/job/123")}
  onMatchCreated={(match) => console.log("Match created:", match)}
/>;
```

### Individual Components

Use individual components with `ProspectingProvider` for custom layouts:

```tsx
import {
  ProspectJobOverview,
  ProspectProviderSearch,
  ProspectList,
  ProspectingProvider,
} from "@/widgets/prospecting";

<ProspectingProvider jobId="job-123">
  <div className="grid grid-cols-4 gap-4">
    <div className="col-span-1">
      <ProspectJobOverview />
      <ProspectProviderSearch />
    </div>
    <div className="col-span-3">
      <ProspectList />
    </div>
  </div>
</ProspectingProvider>;
```

## Features

### ✅ Implemented (Planned)

- **Provider Search**: Search and filter providers by specialty, location, experience
- **Match Creation**: Create matches instead of sending offers
- **Job Context**: Display job details and requirements
- **Provider Cards**: Rich provider information with verification status
- **Real-time Search**: Live search with debouncing and pagination
- **Loading States**: Skeleton loading for all components

### 🚧 Migration Changes

- **Matches API**: Uses new matches API instead of applications/offers
- **Widget Structure**: Follows established widget patterns with context
- **Data/Presentation**: Clear separation between data and presentation layers
- **Composable Design**: Individual components can be used independently

## API Integration

- `api.jobs.get`: Fetch job data with includes
- `api.providers.prospecting.search`: Search providers with filters
- `api.jobs.matches.create`: Create new matches instead of offers
- `api.jobs.matches.list`: List existing matches for the job

## Usage Patterns

### Full Prospecting Interface

Use `ProspectingWidget` for complete prospecting pages:

- Job overview and requirements
- Provider search and filtering
- Provider list with match creation
- Real-time search and pagination

### Custom Layouts

Use individual components for custom prospecting layouts:

- Sidebar with job overview and search
- Main area with provider list
- Modal overlays with focused search

## Migration from Old System

This widget replaces the old prospecting system:

- **Old**: `apps/web-med/src/www/organizations/job/prospecting/`
- **New**: `apps/web-med/src/widgets/prospecting/`

### Key Changes

1. **Matches instead of Offers**: Creates matches directly instead of sending offers
2. **Widget Pattern**: Follows established widget conventions
3. **Context-based**: Uses React Context for data management
4. **Composable**: Individual components can be used independently
5. **Type-safe**: Full TypeScript integration with API types

## Simplicity Principles

Following the "pure simplicity" approach:

1. **Clear Data Flow**: Props flow down, events bubble up
2. **Minimal Dependencies**: Only essential UI components and API calls
3. **Composable Design**: Mix and match components as needed
4. **Consistent Patterns**: Same patterns as other widgets
5. **Real API Integration**: Direct connection to matches API
