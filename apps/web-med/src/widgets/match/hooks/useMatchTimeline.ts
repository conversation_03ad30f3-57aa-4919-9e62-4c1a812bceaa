"use client";

import { useMemo } from "react";

import type { Milestone } from "@axa/ui/blocks/negotiation-timeline";

import type { RouterOutputs } from "@/api";

import { useContextualActions } from "./useContextualActions";

interface UseMatchTimelineProps {
  match: RouterOutputs["jobs"]["matches"]["timeline"]["get"];
  onAcceptRate?: (rate: number) => void;
  onCounterRate?: (rate: number, message?: string) => void;
}

interface UseMatchTimelineReturn {
  milestones: Milestone[];
  contextualActions: {
    actions: ReturnType<typeof useContextualActions>["actions"];
    primaryActions: ReturnType<typeof useContextualActions>["primaryActions"];
    getActionsForGroup: ReturnType<
      typeof useContextualActions
    >["getActionsForGroup"];
    hasActions: ReturnType<typeof useContextualActions>["hasActions"];
    timelineState: ReturnType<typeof useContextualActions>["timelineState"];
  };
}

export function useMatchTimeline({
  match,
  onAcceptRate,
  onCounterRate,
}: UseMatchTimelineProps): UseMatchTimelineReturn {
  const milestones = useMemo(() => {
    if (!match?.blocks) return [];

    // Convert backend TimelineBlock[] to frontend Milestone[] structure
    // Group blocks by logical milestones (rate negotiation, contracts, etc.)

    const milestones: Milestone[] = [];

    // Group blocks by type for milestone organization
    const rateBlocks = match.blocks.filter(
      (block) =>
        block.type.startsWith("rate-") || block.type === "rate-negotiated",
    );
    const messageBlocks = match.blocks.filter(
      (block) => block.type === "message",
    );
    const contractBlocks = match.blocks.filter((block) =>
      block.type.startsWith("contract-"),
    );

    // Rate Negotiation Milestone
    if (rateBlocks.length > 0) {
      // Convert backend rate blocks to frontend format with actions
      const frontendRateBlocks = rateBlocks.map((block) => {
        const frontendBlock = {
          id: block.id,
          type: block.type as any, // Rate block types are compatible
          timestamp: block.timestamp,
          data: { ...block.data },
        };

        // Add action handlers for rate offer blocks
        if (block.type === "rate-offer" && block.data.status === "pending") {
          const rate = block.data.rate || 0;

          if (onAcceptRate) {
            frontendBlock.data.onAccept = () =>
              onAcceptRate(
                typeof rate === "number" ? rate : parseFloat(rate.toString()),
              );
          }

          if (onCounterRate) {
            frontendBlock.data.onCounter = () => {
              const counterRate = prompt(
                `Counter offer (current: $${rate}/hr):`,
              );
              if (counterRate) {
                onCounterRate(parseFloat(counterRate));
              }
            };
          }
        }

        return frontendBlock;
      });

      milestones.push({
        id: "rate-negotiation",
        title: "Rate Negotiation",
        status:
          match.context.compensation?.negotiationStatus === "FINALIZED"
            ? "completed"
            : "active",
        timestamp: rateBlocks[0]?.timestamp || new Date(match.match.createdAt),
        blocks: frontendRateBlocks,
      });
    }

    // Contract Milestone (if contract blocks exist)
    if (contractBlocks.length > 0) {
      milestones.push({
        id: "contracts",
        title: "Contract & Agreements",
        status: "active", // TODO: Determine from contract status
        timestamp: contractBlocks[0]?.timestamp || new Date(),
        blocks: contractBlocks.map((block) => ({
          id: block.id,
          type: block.type as any,
          timestamp: block.timestamp,
          data: block.data,
        })),
      });
    }

    // Communication Milestone (messages)
    if (messageBlocks.length > 0) {
      milestones.push({
        id: "communication",
        title: "Messages",
        status: "completed",
        timestamp:
          messageBlocks[messageBlocks.length - 1]?.timestamp ||
          new Date(match.match.createdAt),
        blocks: messageBlocks.map((block) => ({
          id: block.id,
          type: "message" as const,
          timestamp: block.timestamp,
          data: {
            author: block.data.author || block.actor.name,
            message: block.data.content || "",
            timestamp: block.timestamp,
          },
        })),
      });
    }

    // If no specific blocks, create a basic match milestone
    if (milestones.length === 0) {
      milestones.push({
        id: "match-created",
        title: "Match Created",
        status: "completed",
        timestamp: new Date(match.match.createdAt),
        blocks: [
          {
            id: `match-init-${match.match.id}`,
            type: "message" as const,
            timestamp: new Date(match.match.createdAt),
            data: {
              author:
                match.match.initiator === "PROVIDER"
                  ? "Provider"
                  : "Organization",
              message: "Match initiated for this position.",
              timestamp: new Date(match.match.createdAt),
            },
          },
        ],
      });
    }

    // Sort milestones by timestamp (newest first)
    return milestones.sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),
    );
  }, [match, onAcceptRate, onCounterRate]);

  // Get contextual actions based on the milestones
  const {
    actions,
    primaryActions,
    getActionsForGroup,
    hasActions,
    timelineState,
  } = useContextualActions(milestones);

  return {
    milestones,
    contextualActions: {
      actions,
      primaryActions,
      getActionsForGroup,
      hasActions,
      timelineState,
    },
  };
}
