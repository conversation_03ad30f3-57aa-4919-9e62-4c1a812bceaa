import { useMemo } from "react";

import type { Milestone } from "@axa/ui/blocks/negotiation-timeline";

import { useUser } from "@/components/contexts/User";

import { useMatch } from "../context/MatchContext";

// =====================================================
// ACTION SYSTEM TYPES
// =====================================================

export interface ContextualAction {
  id: string;
  type: ActionType;
  group: ActionGroup;
  priority: ActionPriority;
  label: string;
  description?: string;
  icon?: string;
  variant?: "default" | "primary" | "secondary" | "destructive" | "ghost";
  disabled?: boolean;
  disabledReason?: string;
  requiresConfirmation?: boolean;
  confirmationMessage?: string;
  handler: () => void | Promise<void>;
  metadata?: Record<string, unknown>;
}

export enum ActionType {
  // Rate Actions
  ACCEPT_RATE = "accept_rate",
  COUNTER_RATE = "counter_rate",
  DECLINE_RATE = "decline_rate",
  VIEW_RATE_HISTORY = "view_rate_history",

  // Contract Actions
  CREATE_CONTRACT = "create_contract",
  SIGN_CONTRACT = "sign_contract",
  REVIEW_CONTRACT = "review_contract",
  DOWNLOAD_CONTRACT = "download_contract",
  DECLINE_CONTRACT = "decline_contract",

  // Verification Actions
  START_BACKGROUND_CHECK = "start_background_check",
  UPLOAD_DOCUMENTS = "upload_documents",
  START_IDENTITY_VERIFICATION = "start_identity_verification",
  SETUP_BANK_ACCOUNT = "setup_bank_account",
  RETRY_VERIFICATION = "retry_verification",

  // Communication Actions
  SEND_MESSAGE = "send_message",
  SCHEDULE_CALL = "schedule_call",
  REQUEST_INFO = "request_info",

  // Step Actions
  ADVANCE_STEP = "advance_step",
  COMPLETE_MILESTONE = "complete_milestone",
  MARK_STEP_COMPLETE = "mark_step_complete",

  // Staff Override Actions
  OVERRIDE_VERIFICATION = "override_verification",
  ESCALATE_ISSUE = "escalate_issue",
  MANUAL_APPROVAL = "manual_approval",
  RESET_STEP = "reset_step",

  // General Actions
  VIEW_DETAILS = "view_details",
  EXPORT_DATA = "export_data",
  PRINT_SUMMARY = "print_summary",
}

export enum ActionGroup {
  RATE_NEGOTIATION = "rate_negotiation",
  CONTRACT_MANAGEMENT = "contract_management",
  VERIFICATION = "verification",
  COMMUNICATION = "communication",
  WORKFLOW = "workflow",
  STAFF_TOOLS = "staff_tools",
  GENERAL = "general",
}

export enum ActionPriority {
  CRITICAL = 1, // Immediate action required
  HIGH = 2, // Important next steps
  MEDIUM = 3, // Standard actions
  LOW = 4, // Optional/convenience actions
  BACKGROUND = 5, // Background/admin actions
}

// =====================================================
// TIMELINE STATE ANALYSIS
// =====================================================

export interface TimelineState {
  currentMilestone: Milestone | null;
  currentMilestoneIndex: number;
  currentBlock: any | null; // Current active block within milestone
  currentBlockIndex: number;
  totalMilestones: number;
  completedMilestones: number;
  phase: TimelinePhase;
  isComplete: boolean;
  isPending: boolean;
  requiresUserAction: boolean;
}

export enum TimelinePhase {
  RATE_NEGOTIATION = "rate_negotiation",
  CONTRACT_PHASE = "contract_phase",
  VERIFICATION_PHASE = "verification_phase",
  COMPLETED = "completed",
  STALLED = "stalled",
}

// =====================================================
// CONTEXTUAL ACTION HOOK
// =====================================================

export function useContextualActions(milestones: Milestone[] = []) {
  const user = useUser();
  const match = useMatch();

  return useMemo(() => {
    // Analyze current timeline state
    const timelineState = analyzeTimelineState(milestones);

    // Three-tiered filtration system
    const actions = computeContextualActions(
      user,
      match,
      timelineState,
      milestones,
    );

    // Prioritize and group actions
    const prioritizedActions = prioritizeActions(actions);
    const groupedActions = groupActionsByCategory(prioritizedActions);

    return {
      // Core data
      actions: prioritizedActions,
      groupedActions,
      timelineState,

      // Convenience getters
      primaryActions: prioritizedActions.filter(
        (a) => a.priority <= ActionPriority.HIGH,
      ),
      secondaryActions: prioritizedActions.filter(
        (a) => a.priority > ActionPriority.HIGH,
      ),
      criticalActions: prioritizedActions.filter(
        (a) => a.priority === ActionPriority.CRITICAL,
      ),

      // Action utilities
      hasActions: prioritizedActions.length > 0,
      hasCriticalActions: prioritizedActions.some(
        (a) => a.priority === ActionPriority.CRITICAL,
      ),
      actionCount: prioritizedActions.length,

      // Group utilities
      getActionsForGroup: (group: ActionGroup) => groupedActions[group] || [],
      hasActionsForGroup: (group: ActionGroup) =>
        (groupedActions[group] || []).length > 0,
    };
  }, [user, match, milestones]);
}

// =====================================================
// TIMELINE STATE ANALYSIS
// =====================================================

function analyzeTimelineState(milestones: Milestone[]): TimelineState {
  const totalMilestones = milestones.length;
  const completedMilestones = milestones.filter(
    (m) => m.status === "completed",
  ).length;

  // Find current active milestone
  const currentMilestoneIndex = milestones.findIndex(
    (m) => m.status === "active" || m.status === "pending",
  );

  const currentMilestone =
    currentMilestoneIndex >= 0 && currentMilestoneIndex < milestones.length
      ? milestones[currentMilestoneIndex]
      : null;

  // Find current active block within milestone
  let currentBlock = null;
  let currentBlockIndex = -1;

  if (currentMilestone?.blocks) {
    currentBlockIndex = currentMilestone.blocks.findIndex((block) => {
      // Logic to determine if block is "active" based on its data
      const blockData = block.data as any;
      return blockData?.status === "pending" || blockData?.expectedParty;
    });

    currentBlock =
      currentBlockIndex >= 0 &&
      currentBlockIndex < currentMilestone.blocks.length
        ? currentMilestone.blocks[currentBlockIndex]
        : null;
  }

  // Determine phase
  let phase = TimelinePhase.COMPLETED;
  if (currentMilestone) {
    if (
      currentMilestone.id.includes("rate") ||
      currentMilestone.id.includes("negotiation")
    ) {
      phase = TimelinePhase.RATE_NEGOTIATION;
    } else if (currentMilestone.id.includes("contract")) {
      phase = TimelinePhase.CONTRACT_PHASE;
    } else if (
      currentMilestone.id.includes("verification") ||
      currentMilestone.id.includes("background")
    ) {
      phase = TimelinePhase.VERIFICATION_PHASE;
    } else {
      phase = TimelinePhase.STALLED;
    }
  }

  const isComplete = completedMilestones === totalMilestones;
  const isPending = currentMilestone?.status === "pending";
  const requiresUserAction =
    !!currentBlock && !!(currentBlock.data as any)?.expectedParty;

  return {
    currentMilestone: currentMilestone || null,
    currentMilestoneIndex,
    currentBlock,
    currentBlockIndex,
    totalMilestones,
    completedMilestones,
    phase,
    isComplete,
    isPending,
    requiresUserAction,
  };
}

// =====================================================
// THREE-TIERED FILTRATION SYSTEM
// =====================================================

function computeContextualActions(
  user: any,
  match: any,
  timelineState: TimelineState,
  milestones: Milestone[],
): ContextualAction[] {
  const actions: ContextualAction[] = [];

  // TIER 1: User-based filtration
  const userFilteredActions = getUserAvailableActions(user);

  // TIER 2: Step/Phase-based filtration
  const stepFilteredActions = filterActionsByStep(
    userFilteredActions,
    timelineState,
    match,
  );

  // TIER 3: Block/Context-based filtration
  const contextFilteredActions = filterActionsByContext(
    stepFilteredActions,
    timelineState,
    match,
  );

  return contextFilteredActions;
}

// TIER 1: User-based action filtering
function getUserAvailableActions(user: any): ActionType[] {
  const availableActions: ActionType[] = [];

  // Base actions for all users
  availableActions.push(
    ActionType.SEND_MESSAGE,
    ActionType.VIEW_DETAILS,
    ActionType.VIEW_RATE_HISTORY,
  );

  // Provider-specific actions
  if (user.isProvider) {
    availableActions.push(
      ActionType.COUNTER_RATE,
      ActionType.SIGN_CONTRACT,
      ActionType.START_BACKGROUND_CHECK,
      ActionType.UPLOAD_DOCUMENTS,
      ActionType.START_IDENTITY_VERIFICATION,
      ActionType.SETUP_BANK_ACCOUNT,
      ActionType.RETRY_VERIFICATION,
    );
  }

  // Organization-specific actions
  if (user.isClient || user.organizationId) {
    availableActions.push(
      ActionType.ACCEPT_RATE,
      ActionType.DECLINE_RATE,
      ActionType.CREATE_CONTRACT,
      ActionType.REVIEW_CONTRACT,
      ActionType.SCHEDULE_CALL,
      ActionType.REQUEST_INFO,
    );
  }

  // Staff override actions
  if (user.isInternal || user.isAdmin) {
    availableActions.push(
      ActionType.OVERRIDE_VERIFICATION,
      ActionType.ESCALATE_ISSUE,
      ActionType.MANUAL_APPROVAL,
      ActionType.RESET_STEP,
      ActionType.ADVANCE_STEP,
      ActionType.MARK_STEP_COMPLETE,
      ActionType.EXPORT_DATA,
    );
  }

  return availableActions;
}

// TIER 2: Step/Phase-based filtering
function filterActionsByStep(
  availableActions: ActionType[],
  timelineState: TimelineState,
  match: any,
): ActionType[] {
  const stepFilteredActions: ActionType[] = [];

  for (const actionType of availableActions) {
    let includeAction = false;

    switch (timelineState.phase) {
      case TimelinePhase.RATE_NEGOTIATION:
        includeAction = [
          ActionType.ACCEPT_RATE,
          ActionType.COUNTER_RATE,
          ActionType.DECLINE_RATE,
          ActionType.VIEW_RATE_HISTORY,
          ActionType.SEND_MESSAGE,
          ActionType.VIEW_DETAILS,
          // Staff actions always available
          ActionType.OVERRIDE_VERIFICATION,
          ActionType.ESCALATE_ISSUE,
          ActionType.MANUAL_APPROVAL,
          ActionType.RESET_STEP,
        ].includes(actionType);
        break;

      case TimelinePhase.CONTRACT_PHASE:
        includeAction = [
          ActionType.CREATE_CONTRACT,
          ActionType.SIGN_CONTRACT,
          ActionType.REVIEW_CONTRACT,
          ActionType.DOWNLOAD_CONTRACT,
          ActionType.DECLINE_CONTRACT,
          ActionType.SEND_MESSAGE,
          ActionType.VIEW_DETAILS,
          // Staff actions
          ActionType.OVERRIDE_VERIFICATION,
          ActionType.ESCALATE_ISSUE,
          ActionType.MANUAL_APPROVAL,
          ActionType.ADVANCE_STEP,
        ].includes(actionType);
        break;

      case TimelinePhase.VERIFICATION_PHASE:
        includeAction = [
          ActionType.START_BACKGROUND_CHECK,
          ActionType.UPLOAD_DOCUMENTS,
          ActionType.START_IDENTITY_VERIFICATION,
          ActionType.SETUP_BANK_ACCOUNT,
          ActionType.RETRY_VERIFICATION,
          ActionType.SEND_MESSAGE,
          ActionType.VIEW_DETAILS,
          // Staff actions
          ActionType.OVERRIDE_VERIFICATION,
          ActionType.ESCALATE_ISSUE,
          ActionType.MANUAL_APPROVAL,
          ActionType.MARK_STEP_COMPLETE,
        ].includes(actionType);
        break;

      case TimelinePhase.COMPLETED:
        includeAction = [
          ActionType.VIEW_DETAILS,
          ActionType.EXPORT_DATA,
          ActionType.PRINT_SUMMARY,
          ActionType.DOWNLOAD_CONTRACT,
          // Staff actions
          ActionType.RESET_STEP,
          ActionType.EXPORT_DATA,
        ].includes(actionType);
        break;

      default:
        // General actions always available
        includeAction = [
          ActionType.SEND_MESSAGE,
          ActionType.VIEW_DETAILS,
          ActionType.ESCALATE_ISSUE,
        ].includes(actionType);
    }

    if (includeAction) {
      stepFilteredActions.push(actionType);
    }
  }

  return stepFilteredActions;
}

// TIER 3: Block/Context-based filtering
function filterActionsByContext(
  stepActions: ActionType[],
  timelineState: TimelineState,
  match: any,
): ContextualAction[] {
  const contextualActions: ContextualAction[] = [];

  for (const actionType of stepActions) {
    const action = createContextualAction(actionType, timelineState, match);

    // Only include if action is valid for current context
    if (action && isActionValidForContext(action, timelineState, match)) {
      contextualActions.push(action);
    }
  }

  return contextualActions;
}

// =====================================================
// ACTION CREATION & VALIDATION
// =====================================================

function createContextualAction(
  actionType: ActionType,
  timelineState: TimelineState,
  match: any,
): ContextualAction | null {
  const baseAction = {
    id: `action_${actionType}_${Date.now()}`,
    type: actionType,
    metadata: {
      matchId: match?.match?.id,
      currentPhase: timelineState.phase,
      currentMilestone: timelineState.currentMilestone?.id,
    },
  };

  switch (actionType) {
    case ActionType.ACCEPT_RATE:
      return {
        ...baseAction,
        group: ActionGroup.RATE_NEGOTIATION,
        priority: ActionPriority.CRITICAL,
        label: `Accept $${match?.currentRate}/hr`,
        description: "Accept the current rate offer",
        icon: "check",
        variant: "primary",
        handler: () => match?.acceptRate?.(match.currentRate),
      };

    case ActionType.COUNTER_RATE:
      return {
        ...baseAction,
        group: ActionGroup.RATE_NEGOTIATION,
        priority: ActionPriority.HIGH,
        label: "Counter Offer",
        description: "Propose a different rate",
        icon: "refresh",
        variant: "secondary",
        handler: () => {
          const newRate = prompt(
            `Counter offer (current: $${match?.currentRate}/hr):`,
          );
          if (newRate && !isNaN(parseFloat(newRate))) {
            match?.counterRate?.(parseFloat(newRate));
          }
        },
      };

    case ActionType.CREATE_CONTRACT:
      return {
        ...baseAction,
        group: ActionGroup.CONTRACT_MANAGEMENT,
        priority: ActionPriority.HIGH,
        label: "Create Contract",
        description: "Generate contract for agreed rate",
        icon: "file-text",
        variant: "primary",
        handler: () => console.log("Create contract"),
      };

    case ActionType.START_BACKGROUND_CHECK:
      return {
        ...baseAction,
        group: ActionGroup.VERIFICATION,
        priority: ActionPriority.HIGH,
        label: "Start Background Check",
        description: "Begin verification process",
        icon: "shield-check",
        variant: "primary",
        handler: () => console.log("Start background check"),
      };

    case ActionType.ESCALATE_ISSUE:
      return {
        ...baseAction,
        group: ActionGroup.STAFF_TOOLS,
        priority: ActionPriority.MEDIUM,
        label: "Escalate Issue",
        description: "Flag for supervisor review",
        icon: "alert-triangle",
        variant: "destructive",
        requiresConfirmation: true,
        confirmationMessage:
          "This will escalate the match to a supervisor. Continue?",
        handler: () => console.log("Escalate issue"),
      };

    // Add more action creators as needed...
    default:
      // Generic action creator
      return {
        ...baseAction,
        group: ActionGroup.GENERAL,
        priority: ActionPriority.MEDIUM,
        label: actionType
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase()),
        handler: () => console.log(`Execute action: ${actionType}`),
      };
  }
}

function isActionValidForContext(
  action: ContextualAction,
  timelineState: TimelineState,
  match: any,
): boolean {
  // Add context-specific validation logic
  switch (action.type) {
    case ActionType.ACCEPT_RATE:
      return !!(match?.canAcceptRate && match?.currentRate);

    case ActionType.COUNTER_RATE:
      return !!(match?.canCounterRate && match?.currentRate);

    case ActionType.CREATE_CONTRACT:
      return (
        timelineState.phase === TimelinePhase.RATE_NEGOTIATION &&
        timelineState.currentMilestone?.status === "completed"
      );

    default:
      return true;
  }
}

// =====================================================
// ACTION PRIORITIZATION & GROUPING
// =====================================================

function prioritizeActions(actions: ContextualAction[]): ContextualAction[] {
  return actions.sort((a, b) => {
    // Primary sort: Priority (lower number = higher priority)
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }

    // Secondary sort: Group importance
    const groupPriority = {
      [ActionGroup.RATE_NEGOTIATION]: 1,
      [ActionGroup.CONTRACT_MANAGEMENT]: 2,
      [ActionGroup.VERIFICATION]: 3,
      [ActionGroup.WORKFLOW]: 4,
      [ActionGroup.COMMUNICATION]: 5,
      [ActionGroup.STAFF_TOOLS]: 6,
      [ActionGroup.GENERAL]: 7,
    };

    const aGroupPriority = groupPriority[a.group] || 999;
    const bGroupPriority = groupPriority[b.group] || 999;

    if (aGroupPriority !== bGroupPriority) {
      return aGroupPriority - bGroupPriority;
    }

    // Tertiary sort: Alphabetical
    return a.label.localeCompare(b.label);
  });
}

function groupActionsByCategory(
  actions: ContextualAction[],
): Record<ActionGroup, ContextualAction[]> {
  const grouped = {} as Record<ActionGroup, ContextualAction[]>;

  // Initialize all groups
  Object.values(ActionGroup).forEach((group) => {
    grouped[group] = [];
  });

  // Group actions
  actions.forEach((action) => {
    grouped[action.group].push(action);
  });

  return grouped;
}
