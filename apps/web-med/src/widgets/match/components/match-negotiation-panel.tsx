"use client";

import { useMemo } from "react";
import { MessageSquare } from "lucide-react";

import type {
  MessageBlockData,
  Milestone,
  RateNegotiatedBlockData,
  RateOfferBlockData,
} from "@axa/ui/blocks/negotiation-timeline";
import {
  defaultRegistry,
  NegotiationTimeline,
  rateNegotiationRegistry,
} from "@axa/ui/blocks/negotiation-timeline";

import { useMatch } from "../context/MatchContext";

// Helper function to safely create Date objects
function createSafeDate(dateValue: unknown): Date {
  if (!dateValue) {
    return new Date(); // Fallback to current date
  }

  const date = new Date(dateValue as string | number | Date);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return new Date(); // Fallback to current date
  }

  return date;
}

interface MatchNegotiationPanelProps {
  className?: string;
}

export function MatchNegotiationPanel({
  className,
}: MatchNegotiationPanelProps) {
  const { match, loading } = useMatch();

  // Generate milestones from match data
  const milestones: Milestone[] = useMemo(() => {
    if (!match) return [];

    return [
      {
        id: "rate-negotiation",
        title: "Rate Negotiation",
        status: "completed",
        timestamp: createSafeDate(match.createdAt),
        blocks: [
          {
            id: "rate-offer-1",
            type: "rate-offer",
            timestamp: createSafeDate(match.createdAt),
            data: {
              rate: "$85/hr",
              status: "pending",
              expectedParty: "provider",
              onAccept: () => console.log("Accept rate"),
              onCounter: () => console.log("Counter rate"),
            } as RateOfferBlockData,
          },
          {
            id: "message-1",
            type: "message",
            timestamp: createSafeDate(match.updatedAt),
            data: {
              author: "Dr. Smith",
              message:
                "I'm interested in this position, but would prefer $95/hr given my experience.",
              avatar: "/avatars/doctor.jpg",
              timestamp: createSafeDate(match.updatedAt),
            } as MessageBlockData,
          },
          {
            id: "rate-negotiated-1",
            type: "rate-negotiated",
            timestamp: createSafeDate(match.updatedAt),
            data: {
              doctor: "Dr. Smith",
              rate: "$90/hr",
              message: "Great! We've agreed on $90/hr.",
              onViewAgreement: () => console.log("View agreement"),
              onViewNegotiationHistory: () => console.log("View history"),
            } as RateNegotiatedBlockData,
          },
        ],
      },
    ];
  }, [match]);

  const handleSendMessage = (message: string) => {
    console.log("Send message:", message);
    // TODO: Implement message sending
  };

  return (
    <div className={className}>
      <div className="flex items-center gap-2 border-b bg-background/50 px-4 py-3">
        <MessageSquare className="size-5 text-teal-600" />
        <h2 className="text-lg font-semibold text-foreground">Negotiations</h2>
      </div>

      <NegotiationTimeline
        className="h-full border border-border"
        milestones={milestones}
        registry={rateNegotiationRegistry} // Using the specialized rate negotiation registry
        defaultExpanded={true}
        showMessageInput={true}
        onSendMessage={handleSendMessage}
        loading={loading}
      />
    </div>
  );
}
