"use client";

import { cn } from "@axa/ui/lib";

import { MatchJobCard } from "./match-job-card";
import { MatchNegotiationPanel } from "./match-negotiation-panel";
import { MatchProviderCard } from "./match-provider-card";
import { MatchRateInsights } from "./match-rate-insights";

export interface MatchCockpitProps {
  className?: string;
  onViewJobDetails?: () => void;
  showNegotiationHeader?: boolean;
  defaultNegotiationExpanded?: boolean;
  showMessageInput?: boolean;
}

export function MatchCockpit({
  className,
  onViewJobDetails,
  showNegotiationHeader = true,
  defaultNegotiationExpanded = true,
  showMessageInput = true,
}: MatchCockpitProps) {
  return (
    <div className={cn("grid h-screen grid-cols-12 gap-4 p-4", className)}>
      {/* Left Column - Provider & Job Info */}
      <div className="col-span-3 space-y-4">
        <MatchProviderCard />
        <MatchJobCard onViewDetails={onViewJobDetails} />
      </div>

      {/* Center Column - Rate Insights */}
      <div className="col-span-3">
        <MatchRateInsights />
      </div>

      {/* Right Column - Negotiation Timeline */}
      <div className="col-span-6">
        <MatchNegotiationPanel
          showHeader={showNegotiationHeader}
          defaultExpanded={defaultNegotiationExpanded}
          showMessageInput={showMessageInput}
        />
      </div>
    </div>
  );
}
