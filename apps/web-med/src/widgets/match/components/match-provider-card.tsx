"use client";

import { ProviderCard } from "@axa/ui/blocks/negotiation-center";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { useMatch } from "../context/MatchContext";

export interface MatchProviderCardProps {
  className?: string;
}

export function MatchProviderCard({ className }: MatchProviderCardProps) {
  const { 
    loading,
    providerName,
    providerAvatar,
    providerSpecialty,
  } = useMatch();

  if (loading) {
    return (
      <div className={className}>
        <div className="space-y-4 p-4">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-3/4" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <ProviderCard
        name={providerName}
        avatar={providerAvatar || ""}
        specialty={providerSpecialty}
        experience="5+ years" // TODO: Calculate from provider data
        securityData={{
          identityVerified: true,
          backgroundCheckStatus: "completed",
          credentialsVerified: true,
          insuranceVerified: true,
        }}
        authorized={true}
      />
    </div>
  );
}
