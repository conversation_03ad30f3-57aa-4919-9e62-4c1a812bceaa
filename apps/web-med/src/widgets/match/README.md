# Match Widget Suite

A composable widget suite for managing provider-organization matches using the new Matches API. Built with data/presentation separation using React Context and decomposed into individual kebab-case components.

## Architecture

### Data/Presentation Separation

- **`MatchContext`**: Centralized data management and API calls
- **Individual Components**: Pure presentation components that consume context
- **Composable Design**: Mix and match components for custom layouts

### Component Structure

```
match/
├── context/
│   └── MatchContext.tsx          # Data layer with API integration
├── components/
│   ├── match-provider-card.tsx   # Provider information display
│   ├── match-job-card.tsx        # Job details display
│   ├── match-rate-insights.tsx   # Rate analytics and market data
│   ├── match-negotiation-panel.tsx # Timeline-based negotiation
│   └── match-cockpit.tsx         # Full layout composition
├── MatchWidget.tsx               # Main widget wrapper
└── MatchRateNegotiation.tsx      # Focused rate negotiation
```

## Components

### `MatchWidget`

Complete match management interface with provider info, job details, rate insights, and negotiation timeline.

```tsx
import { MatchWidget } from "@/components/widgets/match";

<MatchWidget
  matchId="match-123"
  onViewJobDetails={() => navigate("/job/123")}
  showNegotiationHeader={true}
  defaultNegotiationExpanded={true}
/>;
```

### `MatchRateNegotiation`

Focused rate negotiation interface using just the negotiation timeline panel.

```tsx
import { MatchRateNegotiation } from "@/components/widgets/match";

<MatchRateNegotiation matchId="match-123" />;
```

### Individual Components

Use individual components with `MatchProvider` for custom layouts:

```tsx
import {
  MatchJobCard,
  MatchNegotiationPanel,
  MatchProvider,
  MatchProviderCard,
  MatchRateInsights,
} from "@/components/widgets/match";

<MatchProvider matchId="match-123">
  <div className="grid grid-cols-3 gap-4">
    <MatchProviderCard />
    <MatchJobCard onViewDetails={() => {}} />
    <MatchRateInsights />
  </div>
  <MatchNegotiationPanel className="mt-4" />
</MatchProvider>;
```

## Features

### ✅ Implemented

- **Rate Negotiation Timeline**: Visual timeline showing offer history and current status
- **Interactive Rate Actions**: Accept/counter rate offers with real API integration
- **Provider & Job Context**: Display provider info and job details in cockpit layout
- **Rate Insights**: Market comparison and demand metrics (placeholder data)
- **Message Support**: Timeline messaging interface
- **Loading States**: Skeleton loading for all components

### 🚧 In Progress

- **Real Market Data**: Connect rate insights to actual market data
- **Message Persistence**: Save and load timeline messages
- **Offer History**: Complete negotiation history with all offers
- **Contract Integration**: Contract creation after rate agreement

### 📋 Planned

- **Step Workflow**: Full match step progression (verification, background check, etc.)
- **Document Management**: Contract signing and document storage
- **Notification Integration**: Real-time updates and notifications
- **Mobile Optimization**: Responsive design for mobile devices

## Architecture

### Data Flow

```
Match API → MatchWidget → CockpitLayout → NegotiationTimeline → Rate Blocks
         → MatchRateNegotiation → NegotiationPanel → Rate Blocks
```

### Block Registry

Uses the negotiation timeline registry pattern:

- `message`: MessageBlock for timeline messages
- `rate-offer`: RateOfferBlock for active rate offers
- `rate-negotiated`: RateNegotiatedBlock for completed negotiations

### API Integration

- `api.matches.matches.get`: Fetch match data with includes
- `api.matches.rates.submitOffer`: Submit new rate offers
- `api.matches.rates.respondToOffer`: Accept/decline/counter offers

## Usage Patterns

### Full Match Management

Use `MatchWidget` for complete match management pages where you need all context:

- Provider profile and verification status
- Job details and requirements
- Rate insights and market data
- Complete negotiation timeline

### Focused Rate Negotiation

Use `MatchRateNegotiation` for focused rate negotiation flows:

- Embedded in larger workflows
- Modal dialogs for quick rate decisions
- Sidebar panels in job management interfaces

### Custom Compositions

Both components are built on the same timeline blocks, so you can:

- Extract individual blocks for custom layouts
- Combine with other negotiation blocks (contracts, verification)
- Create domain-specific negotiation flows

## Integration with Jobs Context

The match widgets are designed to integrate seamlessly with job management:

```tsx
// In job detail pages
<JobDetails job={job} />
<MatchWidget match={activeMatch} />

// In provider dashboards
<ProviderMatches>
  {matches.map(match => (
    <MatchRateNegotiation key={match.id} matchId={match.id} />
  ))}
</ProviderMatches>

// In organization workflows
<OrganizationMatchManagement>
  <MatchWidget match={selectedMatch} />
</OrganizationMatchManagement>
```

## Simplicity Principles

Following the "pure simplicity" approach:

1. **Clear Data Flow**: Props flow down, events bubble up
2. **Minimal Dependencies**: Only essential UI blocks and API calls
3. **Composable Design**: Mix and match components as needed
4. **Consistent Patterns**: Same timeline blocks across all negotiation flows
5. **Real API Integration**: Direct connection to matches API, no mock data

## Next Steps

1. **Test Integration**: Add to existing job management flows
2. **Real Data**: Connect to actual match data and market insights
3. **Step Expansion**: Add other match steps (verification, contracts)
4. **Mobile Polish**: Optimize for mobile negotiation flows
5. **Performance**: Add caching and optimistic updates
