"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  BanknoteIcon,
  ChartBarIcon,
  FoldersIcon,
  GanttChartIcon,
  InboxIcon,
  LayoutDashboardIcon,
  LifeBuoyIcon,
  ScrollTextIcon,
  SettingsIcon,
  SquareUserRoundIcon,
  StethoscopeIcon,
  UserCheckIcon,
  UserIcon,
} from "lucide-react";

import { Emblem } from "@axa/ui/brand/Emblem";
import { Logo, Med } from "@axa/ui/brand/Logo";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from "@axa/ui/primitives/drawer";
import { ThemeToggle } from "@axa/ui/primitives/theme";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@axa/ui/primitives/tooltip";

import UserManager from "@/components/widgets/UserManager";
import UserNotifications from "@/components/widgets/UserNotifications";

import { useUser } from "../contexts/User";
import GradientBackground from "../fx/GradientBackground";

const i18n = {
  en: {
    dashboard: "Dashboard",
    shifts: "Shifts",
    jobs: "Jobs",
    earnings: "Earnings",
    profile: "Profile",
    matches: "Matches",
    contracts: "Contracts",
    account: "Account",
    performance: "Performance",
    help: "Help",
    settings: "Settings",
  },
  links: {
    dashboard: "/providers/app",
    shifts: "/providers/app/shifts",
    jobs: "/providers/app/jobs",
    earnings: "/providers/app/earnings",
    matches: "/providers/app/matches",
    contracts: "/providers/app/contracts",
    performance: "/providers/app/performance",
    profile: "/providers/app/profile",
    account: "/providers/app/account",
    help: "/providers/app/help",
    settings: "/providers/app/settings",
  },
};

const links = {
  dashboard: {
    icon: LayoutDashboardIcon,
    label: i18n.en.dashboard,
    href: i18n.links.dashboard,
  },
  shifts: {
    icon: GanttChartIcon,
    label: i18n.en.shifts,
    href: i18n.links.shifts,
  },
  jobs: {
    icon: StethoscopeIcon,
    label: i18n.en.jobs,
    href: i18n.links.jobs,
  },
  matches: {
    icon: UserCheckIcon,
    label: i18n.en.matches,
    href: i18n.links.matches,
  },
  contracts: {
    icon: ScrollTextIcon,
    label: i18n.en.contracts,
    href: i18n.links.contracts,
  },
  earnings: {
    icon: BanknoteIcon,
    label: i18n.en.earnings,
    href: i18n.links.earnings,
  },
  profile: {
    icon: SquareUserRoundIcon,
    label: i18n.en.profile,
    href: i18n.links.profile,
  },
  performance: {
    icon: ChartBarIcon,
    label: i18n.en.performance,
    href: i18n.links.performance,
  },
  account: {
    icon: UserIcon,
    label: i18n.en.account,
    href: i18n.links.account,
  },
  help: {
    icon: LifeBuoyIcon,
    label: i18n.en.help,
    href: i18n.links.help,
  },
  settings: {
    icon: SettingsIcon,
    label: i18n.en.settings,
    href: i18n.links.settings,
  },
};

const sidebarItems = [
  links.dashboard,
  links.shifts,
  links.jobs,
  links.matches,
  links.contracts,
  links.performance,
  links.earnings,
  links.profile,
];

export function useProviderLayout() {
  const user = useUser();
  const pathname = usePathname();

  return {
    links: sidebarItems.map((item) => ({
      ...item,
      active:
        item.label === "Dashboard"
          ? pathname === item.href
          : pathname.startsWith(item.href),
    })),
  };
}

export default function ProviderDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { links } = useProviderLayout();
  const [open, setOpen] = useState(false);

  return (
    <div className="relative mb-16 grid min-h-screen w-full md:mb-0 md:pl-[80px]">
      <div className="fixed inset-0 z-10">
        <GradientBackground />
      </div>
      {/* Desktop Sidebar */}
      <aside className="fixed inset-y-0 left-0 z-20 hidden h-full flex-col border-r bg-background/90 backdrop-blur-md md:flex">
        <div className="border-b p-4">
          <Button variant="ghost" size="icon" aria-label="Home" asChild>
            <Link href={i18n.links.dashboard}>
              <Emblem className="size-6 fill-foreground" />
            </Link>
          </Button>
        </div>
        <TooltipProvider delayDuration={0}>
          <nav className="grid gap-4 p-4">
            {links.map((item, index) => (
              <Tooltip key={index}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn("rounded-sm", {
                      "bg-primary text-primary-foreground hover:bg-primary/75 hover:text-primary-foreground/80":
                        item.active,
                    })}
                    aria-label={item.label}
                    asChild
                  >
                    <Link href={item.href}>
                      <item.icon className="size-6" />
                    </Link>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right" sideOffset={5}>
                  {item.label}
                </TooltipContent>
              </Tooltip>
            ))}
          </nav>
          <nav className="mt-auto grid gap-2 p-4">
            <ThemeToggle variant="ghost" side="right" />
            <UserNotifications align="end" side="right" />
            <UserManager align="end" side="right" />
          </nav>
        </TooltipProvider>
      </aside>

      {/* Mobile Bottom Navigation */}
      <nav className="fixed inset-x-0 bottom-0 z-20 flex w-full items-center justify-between border-t bg-muted/30 p-3 bg-blend-luminosity backdrop-blur-md md:hidden">
        {links.slice(0, 2).map((item, index) => (
          <Button
            key={index}
            variant="ghost"
            size="icon"
            aria-label={item.label}
            asChild
            className={cn("rounded-md", {
              "bg-primary text-primary-foreground hover:bg-primary/75 hover:text-primary-foreground/80":
                item.active,
            })}
          >
            <Link href={item.href}>
              <item.icon className="size-6" />
            </Link>
          </Button>
        ))}
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              aria-label="Menu"
              className="flex size-16 flex-col items-center justify-center gap-1"
            >
              <Logo className="size-12 fill-foreground" shadow>
                <Med />
              </Logo>
              <div className="mx-auto h-1 w-12 rounded-lg bg-muted-foreground/20" />
            </Button>
          </DrawerTrigger>
          <DrawerContent>
            <div className="grid gap-4 p-4">
              <div className="flex items-center justify-center">
                <Logo className="h-20 w-28">
                  <Med />
                </Logo>
              </div>
              <div className="grid grid-cols-3 gap-4">
                {links.map((item, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className={cn(
                      "flex h-auto flex-col items-center gap-4 rounded-lg py-4",
                      {
                        "bg-primary text-primary-foreground hover:bg-primary/75 hover:text-primary-foreground/80":
                          item.active,
                      },
                    )}
                    asChild
                    onClick={() => {
                      setTimeout(() => {
                        setOpen(false);
                      }, 250);
                    }}
                  >
                    <Link href={item.href}>
                      <item.icon className="size-6" />
                      <span className="text-xs">{item.label}</span>
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          </DrawerContent>
        </Drawer>
        <UserNotifications side="top" />
        <UserManager side="top" />
      </nav>

      <div className="z-10 flex w-full flex-1 flex-col gap-4 p-4 sm:px-6 md:gap-8">
        {children}
      </div>
    </div>
  );
}
