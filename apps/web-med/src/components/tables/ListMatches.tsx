"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import TimeAgo from "@axa/ui/shared/TimeAgo";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { MatchInitiator, MatchStatus } from "@/api";

import ProviderRole from "../common/ProviderRole";

const i18n = {
  en: {
    noData: "There are no matches yet",
    selection: "Selection",
    noProvider: "No provider assigned",
    noOrganization: "No organization assigned",
    noRate: "Rate not set",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search matches...",
      add: "Add Match",
    },
    headers: {
      status: "Status",
      job: "Job",
      provider: "Provider",
      organization: "Organization",
      rate: "Rate",
      initiator: "Initiated By",
      created: "Created",
    },
    filters: {
      status: "Status",
      initiator: "Initiator",
      options: {
        all: "All",
        [MatchStatus.PENDING]: "Pending",
        [MatchStatus.ACCEPTED]: "Accepted",
        [MatchStatus.DECLINED]: "Declined",
        [MatchStatus.VALIDATING]: "Validating",
        [MatchStatus.NEGOTIATING]: "Negotiating",
        [MatchStatus.FINALIZING]: "Finalizing",
        [MatchStatus.MATCHED]: "Matched",
        [MatchStatus.WITHDRAWN]: "Withdrawn",
        [MatchStatus.CANCELLED]: "Cancelled",
        [MatchStatus.EXPIRED]: "Expired",
        [MatchInitiator.PROVIDER]: "Provider",
        [MatchInitiator.ORGANIZATION]: "Organization",
        [MatchInitiator.MUTUAL]: "Mutual",
        [MatchInitiator.REFERRAL]: "Referral",
      },
    },
  },
  links: {
    matches: "/app/matches/[id]",
    jobs: "/app/jobs/[id]",
    providers: "/app/providers/[id]",
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "match";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "PENDING",
        label: i18n.en.filters.options[MatchStatus.PENDING],
      },
      {
        value: "ACCEPTED",
        label: i18n.en.filters.options[MatchStatus.ACCEPTED],
      },
      {
        value: "DECLINED",
        label: i18n.en.filters.options[MatchStatus.DECLINED],
      },
      {
        value: "VALIDATING",
        label: i18n.en.filters.options[MatchStatus.VALIDATING],
      },
      {
        value: "NEGOTIATING",
        label: i18n.en.filters.options[MatchStatus.NEGOTIATING],
      },
      {
        value: "FINALIZING",
        label: i18n.en.filters.options[MatchStatus.FINALIZING],
      },
      {
        value: "MATCHED",
        label: i18n.en.filters.options[MatchStatus.MATCHED],
      },
      {
        value: "WITHDRAWN",
        label: i18n.en.filters.options[MatchStatus.WITHDRAWN],
      },
      {
        value: "CANCELLED",
        label: i18n.en.filters.options[MatchStatus.CANCELLED],
      },
      {
        value: "EXPIRED",
        label: i18n.en.filters.options[MatchStatus.EXPIRED],
      },
    ] satisfies { value: MatchType["status"] | null; label: string }[],
  },
  {
    id: "initiator",
    label: i18n.en.filters.initiator,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.all,
      },
      {
        value: "PROVIDER",
        label: i18n.en.filters.options[MatchInitiator.PROVIDER],
      },
      {
        value: "ORGANIZATION",
        label: i18n.en.filters.options[MatchInitiator.ORGANIZATION],
      },
      {
        value: "MUTUAL",
        label: i18n.en.filters.options[MatchInitiator.MUTUAL],
      },
      {
        value: "REFERRAL",
        label: i18n.en.filters.options[MatchInitiator.REFERRAL],
      },
    ],
  },
];

export type MatchesQueryResult = RouterOutputs["matches"]["list"];
export type MatchesType = MatchesQueryResult["items"];
export type MatchType = MatchesType[number];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListMatchesProps extends PropsWithChildren {
  loading?: boolean;
  matches?: MatchesQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  mode?: "organization" | "provider"; // Add mode support
}

// Helper component for match status badge
function MatchStatusBadge({ status }: { status: MatchStatus }) {
  const statusColors = {
    [MatchStatus.PENDING]: "bg-yellow-100 text-yellow-800",
    [MatchStatus.ACCEPTED]: "bg-green-100 text-green-800",
    [MatchStatus.DECLINED]: "bg-red-100 text-red-800",
    [MatchStatus.VALIDATING]: "bg-blue-100 text-blue-800",
    [MatchStatus.NEGOTIATING]: "bg-orange-100 text-orange-800",
    [MatchStatus.FINALIZING]: "bg-purple-100 text-purple-800",
    [MatchStatus.MATCHED]: "bg-emerald-100 text-emerald-800",
    [MatchStatus.WITHDRAWN]: "bg-gray-100 text-gray-800",
    [MatchStatus.CANCELLED]: "bg-red-100 text-red-800",
    [MatchStatus.EXPIRED]: "bg-gray-100 text-gray-800",
  };

  return (
    <span
      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
        statusColors[status] || "bg-gray-100 text-gray-800"
      }`}
    >
      {i18n.en.filters.options[status] || status}
    </span>
  );
}

// Helper component for provider name
function ProviderName({ provider }: { provider: MatchType["provider"] }) {
  if (!provider?.person) {
    return <span className="text-muted-foreground">{i18n.en.noProvider}</span>;
  }

  const { firstName, lastName } = provider.person;
  const fullName = `${firstName} ${lastName}`.trim();

  return (
    <div className="flex flex-col">
      <span className="font-medium">{fullName}</span>
      {provider.title && (
        <span className="text-sm text-muted-foreground">{provider.title}</span>
      )}
    </div>
  );
}

// Helper component for rate display
function RateDisplay({
  compensation,
}: {
  compensation: MatchType["compensation"];
}) {
  if (!compensation) {
    return <span className="text-muted-foreground">{i18n.en.noRate}</span>;
  }

  const { finalAgreedRate, currentOfferRate, minRate, maxRate } = compensation;

  if (finalAgreedRate) {
    return (
      <div className="flex flex-col">
        <span className="font-medium">${finalAgreedRate}/hr</span>
        <span className="text-sm text-green-600">Agreed</span>
      </div>
    );
  }

  if (currentOfferRate) {
    return (
      <div className="flex flex-col">
        <span className="font-medium">${currentOfferRate}/hr</span>
        <span className="text-sm text-orange-600">Offered</span>
      </div>
    );
  }

  if (minRate && maxRate) {
    return (
      <div className="flex flex-col">
        <span className="font-medium">
          ${minRate}-${maxRate}/hr
        </span>
        <span className="text-sm text-muted-foreground">Range</span>
      </div>
    );
  }

  return <span className="text-muted-foreground">{i18n.en.noRate}</span>;
}

export default function ListMatches({
  group = groupName,
  loading = false,
  matches,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  mode = "organization",
}: ListMatchesProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!matches) return undefined;
    return {
      items: matches.items,
      total: matches.total,
    };
  }, [matches]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filterGroups}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {filters}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<MatchType>(
            ["id", "status", "initiator", "createdAt"],
            {
              filename: "matches_export.csv",
              label: "Export Selected Matches",
              resolvers: {
                createdAt: (date) => {
                  if (date instanceof Date) {
                    return date.toISOString();
                  }
                  return String(date);
                },
              },
            },
          ),
        ],
        [],
      )}
      columns={useMemo(() => {
        const baseColumns = [
          {
            accessorKey: "status",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.status}
              />
            ),
            cell: ({ row }) => (
              <MatchStatusBadge status={row.original.status} />
            ),
          },
          {
            accessorKey: "job",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.job}
              />
            ),
            cell: ({ row }) => {
              const job = row.original.job;
              return job ? (
                <div className="flex w-fit max-w-[200px] flex-col truncate">
                  <Link
                    href={i18n.links.jobs.replace("[id]", job.id)}
                    className="w-full truncate text-nowrap font-semibold hover:text-primary"
                  >
                    {job.summary || "Untitled Job"}
                  </Link>
                  {job.role && <ProviderRole roleName={job.role} />}
                </div>
              ) : (
                <span className="text-muted-foreground">No job</span>
              );
            },
            enableHiding: false,
          },
        ];

        // Add provider column only for organization mode
        if (mode === "organization") {
          baseColumns.push({
            accessorKey: "provider",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.provider}
              />
            ),
            cell: ({ row }) => (
              <ProviderName provider={row.original.provider} />
            ),
          });
        }

        // Add organization column only for provider mode
        if (mode === "provider") {
          baseColumns.push({
            accessorKey: "organization",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.organization}
              />
            ),
            cell: ({ row }) => {
              const org = row.original.organization;
              return org ? (
                <Link
                  href={i18n.links.organizations.replace("[id]", org.id)}
                  className="transition-colors hover:text-primary"
                >
                  <PreviewOrganization loading={loading} organization={org} />
                </Link>
              ) : (
                <span className="text-muted-foreground">
                  {i18n.en.noOrganization}
                </span>
              );
            },
          });
        }

        // Add common columns
        baseColumns.push(
          {
            accessorKey: "compensation",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.rate}
              />
            ),
            cell: ({ row }) => (
              <RateDisplay compensation={row.original.compensation} />
            ),
          },
          {
            accessorKey: "initiator",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.initiator}
              />
            ),
            cell: ({ row }) => (
              <span className="text-sm">
                {i18n.en.filters.options[row.original.initiator] ||
                  row.original.initiator}
              </span>
            ),
          },
          {
            accessorKey: "createdAt",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.created}
              />
            ),
            cell: ({ row }) => <TimeAgo date={row.original.createdAt} />,
          },
        );

        return baseColumns as ColumnDef<MatchType, MatchType[]>[];
      }, [loading, mode])}
    >
      {children}
    </Table>
  );
}
