"use client";

import Link from "next/link";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@axa/ui/primitives/card";
import { Button } from "@axa/ui/primitives/button";
import { Badge } from "@axa/ui/primitives/badge";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import TimeAgo from "@axa/ui/shared/TimeAgo";
import { UserCheckIcon, ClockIcon, CheckCircleIcon, XCircleIcon } from "lucide-react";

import type { RouterOutputs } from "@/api";
import { MatchStatus, MatchInitiator } from "@/api";

const i18n = {
  en: {
    title: "My Matches",
    description: "Recent job matches and applications",
    viewAll: "View All Matches",
    noMatches: "No matches yet",
    noMatchesDescription: "Start applying to jobs to see your matches here.",
    browseJobs: "Browse Jobs",
    stats: {
      pending: "Pending",
      accepted: "Accepted", 
      declined: "Declined",
      total: "Total Matches",
    },
    status: {
      [MatchStatus.PENDING]: "Pending",
      [MatchStatus.ACCEPTED]: "Accepted",
      [MatchStatus.DECLINED]: "Declined",
      [MatchStatus.VALIDATING]: "Validating",
      [MatchStatus.NEGOTIATING]: "Negotiating",
      [MatchStatus.FINALIZING]: "Finalizing",
      [MatchStatus.MATCHED]: "Matched",
      [MatchStatus.WITHDRAWN]: "Withdrawn",
      [MatchStatus.CANCELLED]: "Cancelled",
      [MatchStatus.EXPIRED]: "Expired",
    },
    initiator: {
      [MatchInitiator.PROVIDER]: "Your Application",
      [MatchInitiator.ORGANIZATION]: "Invitation",
      [MatchInitiator.MUTUAL]: "Mutual Interest",
      [MatchInitiator.REFERRAL]: "Referral",
    },
  },
  links: {
    matches: "/providers/app/matches",
    match: "/providers/app/matches/[id]",
    jobs: "/providers/app/jobs",
  },
};

export interface ProviderMatchSummaryProps {
  loading?: boolean;
  matches?: RouterOutputs["providers"]["matches"]["list"];
  onRefresh?: () => void;
}

function MatchStatusBadge({ status }: { status: MatchStatus }) {
  const statusColors = {
    [MatchStatus.PENDING]: "bg-yellow-100 text-yellow-800",
    [MatchStatus.ACCEPTED]: "bg-green-100 text-green-800",
    [MatchStatus.DECLINED]: "bg-red-100 text-red-800",
    [MatchStatus.VALIDATING]: "bg-blue-100 text-blue-800",
    [MatchStatus.NEGOTIATING]: "bg-orange-100 text-orange-800",
    [MatchStatus.FINALIZING]: "bg-purple-100 text-purple-800",
    [MatchStatus.MATCHED]: "bg-emerald-100 text-emerald-800",
    [MatchStatus.WITHDRAWN]: "bg-gray-100 text-gray-800",
    [MatchStatus.CANCELLED]: "bg-red-100 text-red-800",
    [MatchStatus.EXPIRED]: "bg-gray-100 text-gray-800",
  };

  return (
    <Badge
      className={`text-xs ${
        statusColors[status] || "bg-gray-100 text-gray-800"
      }`}
    >
      {i18n.en.status[status] || status}
    </Badge>
  );
}

function MatchSummaryStats({ matches }: { matches: RouterOutputs["providers"]["matches"]["list"] }) {
  const stats = {
    total: matches.total,
    pending: matches.items.filter(m => m.status === MatchStatus.PENDING).length,
    accepted: matches.items.filter(m => m.status === MatchStatus.ACCEPTED).length,
    declined: matches.items.filter(m => m.status === MatchStatus.DECLINED).length,
  };

  return (
    <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
      <div className="text-center">
        <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
        <div className="text-sm text-muted-foreground">{i18n.en.stats.total}</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
        <div className="text-sm text-muted-foreground">{i18n.en.stats.pending}</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-green-600">{stats.accepted}</div>
        <div className="text-sm text-muted-foreground">{i18n.en.stats.accepted}</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-red-600">{stats.declined}</div>
        <div className="text-sm text-muted-foreground">{i18n.en.stats.declined}</div>
      </div>
    </div>
  );
}

function RecentMatchesList({ matches }: { matches: RouterOutputs["providers"]["matches"]["list"] }) {
  const recentMatches = matches.items.slice(0, 5); // Show only 5 most recent

  if (recentMatches.length === 0) {
    return (
      <div className="text-center py-8">
        <UserCheckIcon className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-2 text-sm font-medium text-muted-foreground">
          {i18n.en.noMatches}
        </h3>
        <p className="mt-1 text-sm text-muted-foreground">
          {i18n.en.noMatchesDescription}
        </p>
        <div className="mt-6">
          <Button asChild>
            <Link href={i18n.links.jobs}>
              {i18n.en.browseJobs}
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {recentMatches.map((match) => (
        <Link
          key={match.id}
          href={i18n.links.match.replace("[id]", match.id)}
          className="block"
        >
          <div className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="text-sm font-medium truncate">
                  {match.job?.summary || "Untitled Job"}
                </h4>
                <MatchStatusBadge status={match.status} />
              </div>
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span>{match.organization?.name || "Unknown Organization"}</span>
                <span className="flex items-center gap-1">
                  <ClockIcon className="h-3 w-3" />
                  <TimeAgo date={match.createdAt} />
                </span>
              </div>
              <div className="mt-1">
                <Badge variant="outline" className="text-xs">
                  {i18n.en.initiator[match.initiator] || match.initiator}
                </Badge>
              </div>
            </div>
            <div className="flex items-center">
              {match.status === MatchStatus.PENDING && match.initiator === MatchInitiator.ORGANIZATION && (
                <div className="flex items-center gap-1 text-yellow-600">
                  <ClockIcon className="h-4 w-4" />
                  <span className="text-xs">Action Required</span>
                </div>
              )}
              {match.status === MatchStatus.ACCEPTED && (
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
              )}
              {match.status === MatchStatus.DECLINED && (
                <XCircleIcon className="h-4 w-4 text-red-600" />
              )}
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}

export function ProviderMatchSummarySkeleton() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheckIcon className="h-5 w-5" />
          <Skeleton className="h-5 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="text-center">
              <Skeleton className="h-8 w-12 mx-auto mb-2" />
              <Skeleton className="h-4 w-16 mx-auto" />
            </div>
          ))}
        </div>
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="p-3 rounded-lg border">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <Skeleton className="h-4 w-48 mb-2" />
                  <Skeleton className="h-3 w-32" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            </div>
          ))}
        </div>
        <Skeleton className="h-10 w-full" />
      </CardContent>
    </Card>
  );
}

export default function ProviderMatchSummary({
  loading = false,
  matches,
  onRefresh,
}: ProviderMatchSummaryProps) {
  if (loading || !matches) {
    return <ProviderMatchSummarySkeleton />;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheckIcon className="h-5 w-5" />
          {i18n.en.title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <MatchSummaryStats matches={matches} />
        <RecentMatchesList matches={matches} />
        <Button asChild className="w-full" variant="outline">
          <Link href={i18n.links.matches}>
            {i18n.en.viewAll}
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
