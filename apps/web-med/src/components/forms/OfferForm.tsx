"use client";

import { use<PERSON>allback } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DateField } from "@axa/ui/fields/date-time/Date";
import { TextField } from "@axa/ui/fields/text/Text";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import { SelectJobPostField } from "@/components/selectors/SelectJobPost";
import { SelectProviderField } from "@/components/selectors/SelectProvider";

const i18n = {
  en: {
    fields: {
      notes: {
        label: "Notes",
        placeholder: "Enter notes...",
        description: "Enter notes for the offer...",
      },
      expiresAt: {
        label: "Expiration Date",
        placeholder: "Select expiration date...",
        description: "Select the expiration date for the offer...",
      },
      provider: {
        label: "Provider",
        placeholder: "Select provider...",
        description: "Select the provider for the offer...",
      },
      job: {
        label: "Job",
        placeholder: "Select job...",
        description: "Select the job for the offer...",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

export const offerFormSchema = z.object({
  notes: z.string().min(1, "Notes are required"),
  expiresAt: z.date().optional(),
  providerId: z.string().min(1, "Provider is required"),
  jobId: z.string().min(1, "Job is required"),
});

export type OfferFormValues = z.infer<typeof offerFormSchema>;

export interface OfferFormProps {
  defaultValues?: Partial<OfferFormValues>;
  onSubmit: (values: OfferFormValues) => Promise<void>;
  submitProps?: ButtonProps;
  showProvider?: boolean;
  showJob?: boolean;
}

export default function OfferForm({
  defaultValues,
  onSubmit,
  submitProps,
  showProvider = false,
  showJob = false,
}: OfferFormProps) {
  const form = useForm<OfferFormValues>({
    resolver: zodResolver(offerFormSchema),
    defaultValues: {
      notes: "",
      ...defaultValues,
    },
  });

  const handleSubmit = useCallback(
    async (values: OfferFormValues) => {
      try {
        await onSubmit(values);
        form.reset();
      } catch (error) {
        // Error is handled by the parent component
      }
    },
    [form, onSubmit],
  );

  return (
    <Form {...form}>
      <form
        className="flex flex-col gap-4"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <TextField
          name="notes"
          label={i18n.en.fields.notes.label}
          description={i18n.en.fields.notes.description}
          placeholder={i18n.en.fields.notes.placeholder}
        />

        <DateField
          name="expirationDate"
          label={i18n.en.fields.expiresAt.label}
          description={i18n.en.fields.expiresAt.description}
          placeholder={i18n.en.fields.expiresAt.placeholder}
        />

        {showProvider && (
          <SelectProviderField
            name="providerId"
            label={i18n.en.fields.provider.label}
            description={i18n.en.fields.provider.description}
            placeholder={i18n.en.fields.provider.placeholder}
          />
        )}

        {showJob && (
          <SelectJobPostField
            name="jobId"
            label={i18n.en.fields.job.label}
            description={i18n.en.fields.job.description}
            placeholder={i18n.en.fields.job.placeholder}
          />
        )}

        <Button
          type="submit"
          className="w-full"
          disabled={form.formState.isSubmitting}
          {...submitProps}
        >
          {i18n.en.actions.submit}
        </Button>
      </form>
    </Form>
  );
}
