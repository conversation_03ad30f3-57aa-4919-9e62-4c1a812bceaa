"use client";

import {
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";

import type { MatchInitiator, MatchStatus } from "@/api";

import { api } from "@/api";
import { useUser } from "@/components/contexts/User";

export interface UseListProviderMatchesOptions {
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

export interface UseListProviderMatchesReturn {
  data?: {
    items: Array<{
      id: string;
      status: MatchStatus;
      initiator: MatchInitiator;
      createdAt: Date;
      job?: {
        id: string;
        summary: string;
        role: string;
      };
      organization?: {
        id: string;
        name: string;
        avatar: string | null;
      };
      compensation?: {
        finalAgreedRate?: number;
        currentOfferRate?: number;
        minRate?: number;
        maxRate?: number;
      };
    }>;
    total: number;
  };
  loading: boolean;
  error: any;
  refetch: () => Promise<void>;
}

export function useListProviderMatches(
  options: UseListProviderMatchesOptions = {},
): UseListProviderMatchesReturn {
  const {
    group = "provider-match",
    defaultPageSize = 10,
    defaultPageIndex = 0,
  } = options;

  const user = useUser();

  // Search parameters
  const query = useSearchTextValue(group);
  const pagination = useSearchPaginationValue(
    group,
    "pagination",
    defaultPageSize,
    defaultPageIndex,
  );
  const status = useSearchFilterValue<MatchStatus>("status", group);
  const initiator = useSearchFilterValue<MatchInitiator>("initiator", group);

  // Use core matches API with provider filter
  const matchesQuery = api.jobs.matches.list.useQuery({
    query,
    status,
    initiator,
    providerId: user.providerId || undefined, // Filter by current provider
    pageNumber: pagination.pageIndex,
    pageSize: pagination.pageSize,
    include: {
      job: true,
      organization: true,
      compensation: true,
    },
  });

  return {
    data: matchesQuery.data
      ? {
          items: matchesQuery.data.items,
          total: matchesQuery.data.total,
        }
      : undefined,
    loading: matchesQuery.isLoading,
    error: matchesQuery.error,
    refetch: async () => {
      await matchesQuery.refetch();
    },
  };
}
