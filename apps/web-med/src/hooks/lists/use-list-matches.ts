import type { PaginationState } from "@tanstack/react-table";

import { useCallback, useMemo } from "react";

import {
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";

import type {
  MatchStatus,
  MatchInitiator,
  RouterError,
  RouterOutputs,
} from "@/api";

import { api } from "@/api/client";

interface UseListMatchesOptions {
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  organizationId?: string;
  providerId?: string;
  jobId?: string;
}

interface UseListMatchesReturn {
  data:
    | {
        items: RouterOutputs["matches"]["list"]["items"];
        total: number;
      }
    | undefined;
  loading: boolean;
  error: RouterError;
  refetch: () => Promise<void>;
  searchParams: {
    query: string | undefined;
    pagination: PaginationState;
    status: MatchStatus | undefined;
    initiator: MatchInitiator | undefined;
  };
}

export function useListMatches(
  options: UseListMatchesOptions = {},
): UseListMatchesReturn {
  const {
    group = "match",
    defaultPageSize = 10,
    defaultPageIndex = 0,
    organizationId,
    providerId,
    jobId,
  } = options;

  // Search parameters
  const query = useSearchTextValue(group);
  const pagination = useSearchPaginationValue(
    group,
    "pagination",
    defaultPageSize,
    defaultPageIndex,
  );
  const status = useSearchFilterValue<MatchStatus>("status", group);
  const initiator = useSearchFilterValue<MatchInitiator>("initiator", group);

  // tRPC query
  const matchesQuery = api.matches.list.useQuery({
    query,
    status,
    initiator,
    pageNumber: pagination.pageIndex,
    pageSize: pagination.pageSize,
    organizationId,
    providerId,
    jobId,
    include: {
      job: true,
      organization: true,
      provider: true,
      compensation: true,
    },
  });

  // Data transformation
  const data = useMemo(
    () =>
      matchesQuery.data
        ? {
            items: matchesQuery.data.items,
            total: matchesQuery.data.total,
          }
        : undefined,
    [matchesQuery.data],
  );

  // Refetch function
  const refetch = useCallback(async (): Promise<void> => {
    await matchesQuery.refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchesQuery.refetch]);

  return useMemo(
    () => ({
      data,
      loading: matchesQuery.isLoading,
      error: matchesQuery.error,
      refetch,
      searchParams: {
        query,
        pagination,
        status,
        initiator,
      },
    }),
    [
      data,
      matchesQuery.isLoading,
      matchesQuery.error,
      refetch,
      query,
      pagination,
      status,
      initiator,
    ],
  );
}
