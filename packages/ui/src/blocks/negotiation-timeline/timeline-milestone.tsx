import React from "react";
import { cva } from "class-variance-authority";
import {
  CheckCircle,
  ChevronDown,
  ChevronRight,
  Circle,
  Clock,
} from "lucide-react";

import { cn } from "@/ui/lib";
import { Skeleton } from "@/ui/primitives/skeleton";
import TimeAgo from "@/ui/shared/TimeAgo";

import type { Block, Registry, TimelineSize } from "./timeline-block";

import { TimelineBlock } from "./timeline-block";

// Milestone groups blocks together and can be collapsed/expanded
export interface Milestone {
  id: string;
  title: string;
  status: "completed" | "pending" | "active";
  timestamp: Date;
  blocks: Block[];
}

interface TimelineMilestoneProps {
  milestone: Milestone;
  registry: Registry;
  isExpanded: boolean;
  onToggle: (milestoneId: string) => void;
  isFirst?: boolean;
  isLast?: boolean;
  loading?: boolean;
  size?: TimelineSize;
  variant?: "solid" | "outline" | "ghost";
}

function getStatusIcon(status: Milestone["status"]) {
  switch (status) {
    case "completed":
      return (
        <CheckCircle className="size-4 text-teal-600 dark:text-teal-400" />
      );
    case "active":
      return <Clock className="size-4 text-teal-600 dark:text-teal-400" />;
    case "pending":
      return <Circle className="size-4 text-gray-400 dark:text-gray-500" />;
    default:
      return <Circle className="size-4 text-gray-400 dark:text-gray-500" />;
  }
}

const chevronCircleVariants = cva(
  "relative z-20 flex items-center justify-center rounded-full",
  {
    variants: {
      size: {
        small: "size-8",
        medium: "size-12",
        large: "size-16",
      },
      loading: {
        true: "bg-muted",
        false: "bg-teal-600",
      },
    },
    defaultVariants: {
      size: "medium",
      loading: false,
    },
  },
);

// -----------------------------------------------------------------------------
//  ANATOMICAL SUB-COMPONENTS
// -----------------------------------------------------------------------------

export interface TimelineMilestoneIndicatorProps {
  isExpanded: boolean;
  loading?: boolean;
  size?: TimelineSize;
}

function mapSize(size: TimelineSize = "md"): "small" | "medium" | "large" {
  return size === "sm" ? "small" : size === "lg" ? "large" : "medium";
}

export function TimelineMilestoneIndicator({
  isExpanded,
  loading = false,
  size = "md",
}: TimelineMilestoneIndicatorProps) {
  return (
    <div className="flex w-20 shrink-0 items-center justify-center">
      {loading ? (
        <Skeleton
          className={chevronCircleVariants({
            size: mapSize(size),
            loading: true,
          })}
        />
      ) : (
        <div
          className={chevronCircleVariants({
            size: mapSize(size),
            loading: false,
          })}
        >
          {isExpanded ? (
            <ChevronDown className="size-5 text-background" />
          ) : (
            <ChevronRight className="size-5 text-background" />
          )}
        </div>
      )}
    </div>
  );
}

export interface TimelineMilestoneHeaderProps {
  milestone: Milestone;
  loading?: boolean;
  onToggle: () => void;
  variant?: "solid" | "outline" | "ghost";
}

const headerVariants = cva(
  "relative z-10 mr-4 flex flex-1 cursor-pointer items-center justify-between rounded-lg p-4 transition-all duration-300",
  {
    variants: {
      variant: {
        solid:
          "bg-teal-100 hover:bg-teal-200 dark:bg-teal-900/30 dark:hover:bg-teal-800/40",
        outline: "border border-teal-300 dark:border-teal-700",
        ghost: "bg-transparent hover:bg-teal-50 dark:hover:bg-teal-800/20",
      },
      loading: {
        true: "bg-muted",
        false: "",
      },
    },
    defaultVariants: {
      variant: "solid",
      loading: false,
    },
  },
);

export function TimelineMilestoneHeader({
  milestone,
  loading = false,
  onToggle,
  variant = "solid",
}: TimelineMilestoneHeaderProps) {
  return (
    <div
      className={headerVariants({ variant, loading })}
      onClick={loading ? undefined : onToggle}
    >
      <div className="flex items-center gap-3">
        {loading ? (
          <Skeleton className="size-4 rounded-full" />
        ) : (
          getStatusIcon(milestone.status)
        )}
        {loading ? (
          <Skeleton className="h-5 w-32" />
        ) : (
          <span className="font-semibold text-gray-900 dark:text-gray-100">
            {milestone.title}
          </span>
        )}
      </div>
      {loading ? (
        <Skeleton className="h-4 w-16" />
      ) : (
        <TimeAgo
          date={milestone.timestamp}
          className="font-medium text-teal-600 dark:text-teal-400"
        />
      )}
    </div>
  );
}

export function TimelineMilestone({
  milestone,
  registry,
  isExpanded,
  onToggle,
  isFirst = false,
  isLast = false,
  loading = false,
  size = "md",
  variant = "solid",
}: TimelineMilestoneProps) {
  return (
    <div className="relative">
      {/* Milestone Header with 80px left gutter system */}
      <div className="relative flex items-center">
        <TimelineMilestoneIndicator
          isExpanded={isExpanded}
          loading={loading}
          size={size}
        />

        <TimelineMilestoneHeader
          milestone={milestone}
          loading={loading}
          onToggle={() => onToggle(milestone.id)}
          variant={variant}
        />
      </div>

      {/* Collapsible Content */}
      <div
        className={cn(
          "overflow-hidden transition-all duration-500 ease-in-out",
          isExpanded ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0",
        )}
      >
        {/* Section Content */}
        <div className="relative z-10">
          <div className="space-y-0 bg-gray-50/50 pb-4 dark:bg-gray-800/30">
            {milestone.blocks.map((block, blockIndex) => (
              <TimelineBlock
                key={block.id}
                block={block}
                registry={registry}
                isLast={blockIndex === milestone.blocks.length - 1}
                loading={loading}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
