import { <PERSON><PERSON><PERSON>he<PERSON> } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import { Card } from "@/ui/primitives/card";
import { Skeleton } from "@/ui/primitives/skeleton";

interface IdentityVerificationBlockData {
  status: "pending" | "in_progress" | "completed" | "failed";
  verificationType?: "id_document" | "ssn" | "address" | "full";
  provider?: string;
  expectedParty?: "provider" | "organization";
  onStartVerification?: () => void;
  onRetry?: () => void;
  onViewDetails?: () => void;
}

interface IdentityVerificationBlockProps {
  data: IdentityVerificationBlockData;
  actor?: import("../timeline-block").TimelineActor;
  loading?: boolean;
}

export function IdentityVerificationBlock({
  data,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  actor,
  loading = false,
}: IdentityVerificationBlockProps) {
  const getStatusBadge = () => {
    switch (data.status) {
      case "pending":
        return (
          <Badge
            variant="secondary"
            className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
          >
            Pending
          </Badge>
        );
      case "in_progress":
        return (
          <Badge
            variant="secondary"
            className="bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400"
          >
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
          >
            Verified
          </Badge>
        );
      case "failed":
        return (
          <Badge
            variant="destructive"
            className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
          >
            Failed
          </Badge>
        );
    }
  };

  const getVerificationTypeLabel = () => {
    switch (data.verificationType) {
      case "id_document":
        return "ID Document";
      case "ssn":
        return "SSN Verification";
      case "address":
        return "Address Verification";
      case "full":
        return "Full Identity Verification";
      default:
        return "Identity Verification";
    }
  };

  const renderActions = () => {
    if (data.status === "pending" && data.onStartVerification) {
      return (
        <div className="mt-4 flex justify-center">
          <Button
            size="sm"
            className="bg-purple-600 hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-600"
            onClick={data.onStartVerification}
          >
            Start Verification
          </Button>
        </div>
      );
    }

    if (data.status === "failed" && data.onRetry) {
      return (
        <div className="mt-4 flex justify-center gap-2">
          <Button variant="outline" size="sm" onClick={data.onViewDetails}>
            View Details
          </Button>
          <Button
            size="sm"
            className="bg-purple-600 hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-600"
            onClick={data.onRetry}
          >
            Retry Verification
          </Button>
        </div>
      );
    }

    if (data.status === "completed" && data.onViewDetails) {
      return (
        <div className="mt-4 flex justify-center">
          <Button variant="outline" size="sm" onClick={data.onViewDetails}>
            View Verification
          </Button>
        </div>
      );
    }

    return null;
  };

  const renderPlaceholder = () => {
    if (!data.expectedParty) return null;

    return (
      <div className="mt-4 rounded-md bg-gray-50 p-3 text-center dark:bg-gray-800">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Waiting for{" "}
          <span className="font-medium">
            {data.expectedParty === "provider" ? "provider" : "organization"}
          </span>{" "}
          to complete identity verification
        </p>
      </div>
    );
  };

  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-purple-500">
            <UserCheck className="size-6 text-white" />
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <Card className="border-purple-200 bg-purple-50 p-4 dark:border-purple-800 dark:bg-purple-900/20">
          <div className="mb-2 flex items-center justify-between">
            {loading ? (
              <>
                <Skeleton className="h-5 w-36" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </>
            ) : (
              <>
                <p className="font-semibold text-purple-800 dark:text-purple-400">
                  {getVerificationTypeLabel()}
                </p>
                {getStatusBadge()}
              </>
            )}
          </div>

          {loading ? (
            <Skeleton className="h-4 w-32" />
          ) : (
            data.provider && (
              <p className="text-gray-600 dark:text-gray-400">
                Provider: {data.provider}
              </p>
            )
          )}

          {loading ? (
            <div className="mt-4 flex justify-center">
              <Skeleton className="h-8 w-32" />
            </div>
          ) : (
            <>
              {renderActions()}
              {renderPlaceholder()}
            </>
          )}
        </Card>
      </div>
    </div>
  );
}

export type { IdentityVerificationBlockData, IdentityVerificationBlockProps };
