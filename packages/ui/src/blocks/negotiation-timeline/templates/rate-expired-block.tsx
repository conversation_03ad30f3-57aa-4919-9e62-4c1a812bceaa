import { Clock } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import { Skeleton } from "@/ui/primitives/skeleton";

interface RateExpiredBlockData {
  rate: number;
  formattedRate: string;
  madeBy: "PROVIDER" | "ORGANIZATION";
  expirationDate?: string;
  negotiationRound: number;
  status: "expired";
  onMakeNewOffer?: () => void;
  onViewHistory?: () => void;
}

interface RateExpiredBlockProps {
  data: RateExpiredBlockData;
  actor?: import("../timeline-block").TimelineActor;
  loading?: boolean;
}

export function RateExpiredBlock({
  data,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  actor,
  loading = false,
}: RateExpiredBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-gray-500">
            <Clock className="size-6 text-white" />
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900/20">
          <div className="text-center">
            <div className="mb-2 flex items-center justify-between">
              {loading ? (
                <Skeleton className="h-5 w-24" />
              ) : (
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Rate Offer Expired
                </h4>
              )}
              {loading ? (
                <Skeleton className="h-5 w-16 rounded-full" />
              ) : (
                <Badge
                  variant="secondary"
                  className="bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                >
                  Expired
                </Badge>
              )}
            </div>

            {loading ? (
              <div className="mb-2">
                <Skeleton className="mx-auto h-8 w-32" />
              </div>
            ) : (
              <div className="mb-2 text-2xl font-bold text-gray-900 dark:text-gray-100">
                {data.formattedRate}
              </div>
            )}

            {loading ? (
              <div className="mb-4 space-y-2">
                <Skeleton className="mx-auto h-4 w-48" />
                <Skeleton className="mx-auto h-4 w-36" />
              </div>
            ) : (
              <div className="mb-4 space-y-2">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Offer made by{" "}
                  <span className="font-medium">
                    {data.madeBy === "PROVIDER" ? "provider" : "organization"}
                  </span>{" "}
                  expired without response
                </p>

                {data.expirationDate && (
                  <div className="rounded-md bg-gray-100 p-3 dark:bg-gray-900/30">
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      <strong>Expired on:</strong>{" "}
                      {new Date(data.expirationDate).toLocaleDateString()} at{" "}
                      {new Date(data.expirationDate).toLocaleTimeString()}
                    </p>
                  </div>
                )}
              </div>
            )}

            {loading ? (
              <div className="flex justify-center gap-3">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-20" />
              </div>
            ) : (
              (data.onMakeNewOffer || data.onViewHistory) && (
                <div className="flex justify-center gap-2">
                  {data.onMakeNewOffer && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-teal-300 text-teal-700 hover:bg-teal-50 dark:border-teal-600 dark:text-teal-400 dark:hover:bg-teal-900/30"
                      onClick={data.onMakeNewOffer}
                    >
                      New Offer
                    </Button>
                  )}
                  {data.onViewHistory && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-900/30"
                      onClick={data.onViewHistory}
                    >
                      View History
                    </Button>
                  )}
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export type { RateExpiredBlockData, RateExpiredBlockProps };
