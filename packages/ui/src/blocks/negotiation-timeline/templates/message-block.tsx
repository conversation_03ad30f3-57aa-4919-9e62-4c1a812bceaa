import { Avatar, AvatarFallback, AvatarImage } from "@/ui/primitives/avatar";
import { Skeleton } from "@/ui/primitives/skeleton";
import TimeAgo from "@/ui/shared/TimeAgo";

import type { TimelineSize } from "../timeline-block";

import {
  TimelineBlockContainer,
  TimelineBlockContent,
  TimelineBlockIndicator,
} from "../timeline-block";

interface MessageBlockData {
  author: string;
  message: string;
  avatar?: string;
  timestamp?: Date;
}

interface MessageBlockProps {
  data: MessageBlockData;
  loading?: boolean;
  size?: TimelineSize;
}

export function MessageBlock({
  data,
  loading = false,
  size = "md",
}: MessageBlockProps) {
  const avatarNode = (
    <Avatar className="size-full">
      <AvatarImage src={data.avatar} alt={data.author} />
      <AvatarFallback className="text-sm font-medium">
        {data.author.charAt(0).toUpperCase()}
      </AvatarFallback>
    </Avatar>
  );

  return (
    <TimelineBlockContainer>
      <TimelineBlockIndicator
        size={size}
        loading={loading}
        indicator={avatarNode}
      />

      <TimelineBlockContent loading={loading} variant="ghost">
        <div className="flex items-baseline gap-2">
          {loading ? (
            <Skeleton className="h-5 w-24" />
          ) : (
            <p className="truncate font-semibold text-gray-900 dark:text-gray-100">
              {data.author}
            </p>
          )}
          {loading ? (
            <Skeleton className="h-4 w-16" />
          ) : (
            data.timestamp && (
              <TimeAgo
                date={data.timestamp}
                className="shrink-0 text-xs text-gray-500 dark:text-gray-400"
              />
            )
          )}
        </div>
        {loading ? (
          <div className="mt-1 space-y-1">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        ) : (
          <p className="mt-1 leading-relaxed text-gray-700 dark:text-gray-300">
            {data.message}
          </p>
        )}
      </TimelineBlockContent>
    </TimelineBlockContainer>
  );
}

export type { MessageBlockData, MessageBlockProps };
