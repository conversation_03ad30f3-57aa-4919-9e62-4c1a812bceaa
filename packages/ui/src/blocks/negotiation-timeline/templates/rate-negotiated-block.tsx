import { CheckCircle } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";
import { <PERSON>ton } from "@/ui/primitives/button";
import { Skeleton } from "@/ui/primitives/skeleton";

import type { TimelineActor } from "../timeline-block";

interface RateNegotiatedBlockData {
  doctor: string;
  rate: string;
  message?: string;
  onViewAgreement?: () => void;
  onViewNegotiationHistory?: () => void;
}

interface RateNegotiatedBlockProps {
  data: RateNegotiatedBlockData;
  actor?: TimelineActor;
  loading?: boolean;
}

export function RateNegotiatedBlock({
  data,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  actor,
  loading = false,
}: RateNegotiatedBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        {loading ? (
          <Skeleton className="size-12 rounded-full" />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-green-500">
            <CheckCircle className="size-6 text-white" />
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20">
          <div className="flex flex-wrap items-center gap-2">
            {loading ? (
              <>
                <Skeleton className="h-5 w-40" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </>
            ) : (
              <>
                <p className="font-semibold text-green-800 dark:text-green-400">
                  {data.doctor} agreed to {data.rate}
                </p>
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-400"
                >
                  Completed
                </Badge>
              </>
            )}
          </div>
          {loading ? (
            <div className="mt-2 space-y-1">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          ) : (
            data.message && (
              <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
                {data.message}
              </p>
            )
          )}

          {/* Actions */}
          {loading ? (
            <div className="mt-4 flex justify-center gap-2">
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-8 w-24" />
            </div>
          ) : (
            (data.onViewAgreement || data.onViewNegotiationHistory) && (
              <div className="mt-4 flex justify-center gap-2">
                {data.onViewAgreement && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={data.onViewAgreement}
                  >
                    View Agreement
                  </Button>
                )}
                {data.onViewNegotiationHistory && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={data.onViewNegotiationHistory}
                  >
                    View History
                  </Button>
                )}
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
}

export type { RateNegotiatedBlockData, RateNegotiatedBlockProps };
