import { DollarSign } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import { Skeleton } from "@/ui/primitives/skeleton";

import type { TimelineSize } from "../timeline-block";

import {
  TimelineBlockContainer,
  TimelineBlockContent,
  TimelineBlockIndicator,
} from "../timeline-block";

interface RateOfferBlockData {
  rate: string;
  status: "pending" | "accepted" | "countered";
  expectedParty?: "provider" | "organization";
  onAccept?: () => void;
  onCounter?: () => void;
}

interface RateOfferBlockProps {
  data: RateOfferBlockData;
  loading?: boolean;
  size?: TimelineSize;
  actor?: import("../timeline-block").TimelineActor;
}

export function RateOfferBlock({
  data,
  loading = false,
  size = "md",
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  actor,
}: RateOfferBlockProps) {
  return (
    <TimelineBlockContainer>
      <TimelineBlockIndicator
        size={size}
        loading={loading}
        indicator={<DollarSign className="size-6 text-white" />}
        indicatorClassName="bg-green-500"
      />

      <TimelineBlockContent loading={loading} variant="solid">
        <div className="text-center">
          <div className="mb-2 flex items-center justify-between">
            {loading ? (
              <Skeleton className="h-5 w-16" />
            ) : (
              <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Rate Offer
              </h4>
            )}
            {loading ? (
              <Skeleton className="h-5 w-16 rounded-full" />
            ) : (
              data.status !== "pending" && (
                <Badge
                  variant={data.status === "accepted" ? "default" : "secondary"}
                  className={
                    data.status === "accepted"
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                  }
                >
                  {data.status === "accepted" ? "Accepted" : "Countered"}
                </Badge>
              )
            )}
          </div>

          {loading ? (
            <Skeleton className="mx-auto mb-4 h-8 w-24" />
          ) : (
            <div className="mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100">
              {data.rate}
            </div>
          )}

          {loading ? (
            <div className="flex justify-center gap-3">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
          ) : (
            <>
              {data.status === "pending" &&
                !data.expectedParty &&
                (data.onAccept || data.onCounter) && (
                  <div className="flex justify-center gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-teal-300 text-teal-700 hover:bg-teal-50 dark:border-teal-600 dark:text-teal-400 dark:hover:bg-teal-900/30"
                      onClick={data.onAccept}
                    >
                      Accept
                    </Button>
                    <Button
                      size="sm"
                      className="bg-teal-600 hover:bg-teal-700 dark:bg-teal-700 dark:hover:bg-teal-600"
                      onClick={data.onCounter}
                    >
                      Counter
                    </Button>
                  </div>
                )}

              {data.status === "pending" && data.expectedParty && (
                <div className="mt-4 rounded-md bg-gray-50 p-3 text-center dark:bg-gray-800">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Waiting for{" "}
                    <span className="font-medium">
                      {data.expectedParty === "provider"
                        ? "provider"
                        : "organization"}
                    </span>{" "}
                    to respond to rate offer
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </TimelineBlockContent>
    </TimelineBlockContainer>
  );
}

export type { RateOfferBlockData, RateOfferBlockProps };
