import React from "react";
import { cva } from "class-variance-authority";

import { cn } from "@/ui/lib";
import { Skeleton } from "@/ui/primitives/skeleton";

export type TimelineSize = "sm" | "md" | "lg";

export interface Block {
  id: string;
  type: string;
  timestamp: Date;
  data: unknown;
}

export interface RegistryItem {
  type: string;
  render: (block: Block, loading?: boolean) => React.ReactNode;
}

// Registry maps block types to their renderers
export type Registry = Record<string, RegistryItem>;

// Content (card) visual variants
export type TimelineBlockVariant = "solid" | "outline" | "ghost";

interface TimelineBlockProps {
  block: Block;
  registry: Registry;
  isLast?: boolean;
  loading?: boolean;
  size?: TimelineSize;
  indicatorClassName?: string;
  indicator?: React.ReactNode;
  direction?: "ltr" | "rtl";
  variant?: "solid" | "outline" | "ghost";
}

const indicatorVariants = cva(
  "flex items-center justify-center rounded-full border-2 border-muted bg-muted",
  {
    variants: {
      size: {
        sm: "size-8",
        md: "size-12",
        lg: "size-16",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const contentVariants = cva("rounded-lg p-4 transition-colors", {
  variants: {
    variant: {
      solid: "border bg-card shadow-sm",
      outline: "border border-muted bg-transparent",
      ghost: "border-transparent bg-transparent shadow-none",
    },
    loading: {
      true: "bg-muted",
      false: "",
    },
  },
  compoundVariants: [
    {
      variant: "ghost",
      loading: true,
      className: "bg-muted/50",
    },
  ],
  defaultVariants: {
    variant: "solid",
    loading: false,
  },
});

function DefaultBlockRenderer({
  block,
  loading = false,
  size = "md",
  indicatorClassName,
  indicator,
  direction = "ltr",
  variant = "solid",
}: {
  block: Block;
  loading?: boolean;
  size?: TimelineSize;
  indicatorClassName?: string;
  indicator?: React.ReactNode;
  direction?: "ltr" | "rtl";
  variant?: TimelineBlockVariant;
}) {
  return (
    <TimelineBlockContainer direction={direction}>
      {/* Gutter housing the indicator */}
      <TimelineBlockIndicator
        loading={loading}
        size={size}
        indicator={indicator}
        indicatorClassName={indicatorClassName}
      />

      {/* Content area */}
      <TimelineBlockContent
        loading={loading}
        variant={variant}
        direction={direction}
      >
        <div className="flex items-center justify-between">
          {loading ? (
            <>
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-16" />
            </>
          ) : (
            <>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Unknown Block Type: {block.type}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {block.timestamp.toLocaleTimeString()}
              </span>
            </>
          )}
        </div>
        {!loading && block.data != null && (
          <pre className="mt-2 overflow-x-auto text-xs text-gray-600 dark:text-gray-300">
            {JSON.stringify(block.data, null, 2)}
          </pre>
        )}
        {loading && (
          <div className="mt-2 space-y-1">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-3/4" />
          </div>
        )}
      </TimelineBlockContent>
    </TimelineBlockContainer>
  );
}

export function TimelineBlock({
  block,
  registry,
  isLast = false,
  loading = false,
  size = "md",
  indicatorClassName,
  indicator,
  direction = "ltr",
  variant = "solid",
}: TimelineBlockProps) {
  const registryItem = registry[block.type];

  return (
    <div className="relative">
      {registryItem ? (
        registryItem.render(block, loading)
      ) : (
        <>
          <DefaultBlockRenderer
            block={block}
            loading={loading}
            size={size}
            indicatorClassName={indicatorClassName}
            indicator={indicator}
            direction={direction}
            variant={variant}
          />
          {process.env.NODE_ENV === "development" &&
            (() => {
              console.warn(
                `No registry item found for block type: ${block.type}`,
              );
              return null;
            })()}
        </>
      )}
    </div>
  );
}

// -----------------------------------------------------------------------------
//  PUBLICLY EXPORTED ANATOMICAL SUB-COMPONENTS
// -----------------------------------------------------------------------------

export interface TimelineBlockContainerProps {
  /** Row direction – mirrors layout for RTL */
  direction?: "ltr" | "rtl";
  children: React.ReactNode;
  className?: string;
}

export function TimelineBlockContainer({
  direction = "ltr",
  children,
  className,
}: TimelineBlockContainerProps) {
  return (
    <div
      className={cn(
        "flex items-start py-4",
        direction === "rtl" ? "flex-row-reverse" : "flex-row",
        className,
      )}
    >
      {children}
    </div>
  );
}

export interface TimelineBlockIndicatorProps {
  loading?: boolean;
  size?: TimelineSize;
  indicator?: React.ReactNode;
  indicatorClassName?: string;
  className?: string;
}

export function TimelineBlockIndicator({
  loading = false,
  size = "md",
  indicator,
  indicatorClassName,
  className,
}: TimelineBlockIndicatorProps) {
  return (
    <div className={cn(indicatorVariants({ size }), className)}>
      {loading ? (
        <Skeleton className={cn(indicatorVariants({ size }), "rounded-full")} />
      ) : (
        <div className={cn(indicatorVariants({ size }), indicatorClassName)}>
          {indicator ?? (
            <span className="text-xs text-muted-foreground">?</span>
          )}
        </div>
      )}
    </div>
  );
}

export interface TimelineBlockContentProps {
  loading?: boolean;
  variant?: TimelineBlockVariant;
  direction?: "ltr" | "rtl";
  children?: React.ReactNode;
  className?: string;
}

export function TimelineBlockContent({
  loading = false,
  variant = "solid",
  direction = "ltr",
  children,
  className,
}: TimelineBlockContentProps) {
  return (
    <div
      className={cn("flex-1", direction === "rtl" ? "pl-4" : "pr-4", className)}
    >
      <div className={contentVariants({ variant, loading })}>{children}</div>
    </div>
  );
}
