import { faker } from "@faker-js/faker";

import type {
  Block,
  TimelineActor,
} from "@/ui/blocks/negotiation-timeline/timeline-block";

// =====================================================
// MOCK ACTORS
// =====================================================

export const mockProviderActor: TimelineActor = {
  id: "provider-1",
  name: faker.person.fullName(),
  type: "PROVIDER",
  avatar:
    "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=64&h=64&fit=crop&crop=face",
};

export const mockOrganizationActor: TimelineActor = {
  id: "org-1",
  name: "Metro General Hospital",
  type: "ORGANIZATION",
  avatar:
    "https://images.unsplash.com/photo-**********-6edba6dacbb1?w=64&h=64&fit=crop",
};

export const mockSystemActor: TimelineActor = {
  id: "system",
  name: "System",
  type: "SYSTEM",
};

// =====================================================
// RATE NEGOTIATION MOCK DATA
// =====================================================

export const mockRateOfferBlock: Block = {
  id: "rate-offer-1",
  type: "rate-offer",
  timestamp: faker.date.recent(),
  actor: mockOrganizationActor,
  data: {
    rate: 85,
    formattedRate: "$85/hr",
    offerType: "INITIAL",
    message:
      "We'd like to offer you this position at our standard rate for emergency medicine physicians.",
    expiresAt: faker.date.future(),
    status: "pending",
  },
  metadata: {
    matchId: "match-1",
    compensationId: "comp-1",
    priority: faker.helpers.arrayElement(["high", "medium", "low"]),
    category: "action_required",
  },
};

export const mockRateCounterBlock: Block = {
  id: "rate-counter-1",
  type: "rate-counter",
  timestamp: new Date("2024-01-15T14:30:00Z"),
  actor: mockProviderActor,
  data: {
    rate: 95,
    formattedRate: "$95/hr",
    previousRate: 85,
    message:
      "Thank you for the offer. Based on my experience and the current market rate for emergency medicine, I'd like to counter at $95/hr.",
    expiresAt: "2024-01-17T14:30:00Z",
    status: "pending",
  },
  metadata: {
    matchId: "match-1",
    compensationId: "comp-1",
    priority: "high",
    category: "action_required",
  },
};

export const mockRateAcceptedBlock: Block = {
  id: "rate-accepted-1",
  type: "rate-accepted",
  timestamp: new Date("2024-01-15T16:45:00Z"),
  actor: mockOrganizationActor,
  data: {
    rate: 95,
    formattedRate: "$95/hr",
    message:
      "We accept your counter offer. Looking forward to working with you!",
    status: "accepted",
  },
  metadata: {
    matchId: "match-1",
    compensationId: "comp-1",
    priority: "medium",
    category: "completed",
  },
};

export const mockRateDeclinedBlock: Block = {
  id: "rate-declined-1",
  type: "rate-declined",
  timestamp: faker.date.recent(),
  actor: mockOrganizationActor,
  data: {
    rate: 95,
    formattedRate: "$95/hr",
    message:
      "We appreciate your interest but cannot meet this rate at this time.",
    reason: "Budget constraints for this quarter",
    status: "declined",
  },
  metadata: {
    matchId: "match-1",
    compensationId: "comp-1",
    priority: "medium",
    category: "completed",
  },
};

export const mockRateExpiredBlock: Block = {
  id: "rate-expired-1",
  type: "rate-expired",
  timestamp: faker.date.recent(),
  actor: mockSystemActor,
  data: {
    rate: 85,
    formattedRate: "$85/hr",
    expirationDate: faker.date.future(),
    status: "expired",
  },
  metadata: {
    matchId: "match-1",
    compensationId: "comp-1",
    priority: "medium",
    category: "informational",
  },
};

// =====================================================
// CONTRACT MOCK DATA
// =====================================================

export const mockContractCreatedBlock: Block = {
  id: "contract-created-1",
  type: "contract-created",
  timestamp: faker.date.recent(),
  actor: mockOrganizationActor,
  data: {
    contractType: "Employment Agreement",
    title: "Emergency Medicine Physician Contract",
    status: "pending",
    requiresSignatures: true,
    signatureCount: 0,
  },
  metadata: {
    matchId: "match-1",
    contractId: "contract-1",
    priority: "high",
    category: "action_required",
  },
};

export const mockContractSignedBlock: Block = {
  id: "contract-signed-1",
  type: "contract-signed",
  timestamp: faker.date.recent(),
  actor: mockProviderActor,
  data: {
    contractType: "Employment Agreement",
    title: "Emergency Medicine Physician Contract",
    finalStatus: "signed",
    completedAt: faker.date.recent(),
    allSigners: [
      { name: faker.person.fullName(), role: "Provider", status: "signed" },
      {
        name: "Metro General Hospital",
        role: "Organization",
        status: "signed",
      },
    ],
  },
  metadata: {
    matchId: "match-1",
    contractId: "contract-1",
    priority: "medium",
    category: "completed",
  },
};

// =====================================================
// VERIFICATION MOCK DATA
// =====================================================

export const mockBackgroundCheckBlock: Block = {
  id: "background-check-1",
  type: "verification-started",
  timestamp: faker.date.recent(),
  actor: mockSystemActor,
  data: {
    verificationType: "background",
    status: "in_progress",
    provider: "Sterling Background Checks",
    details:
      "Standard background check including criminal history and professional references",
  },
  metadata: {
    matchId: "match-1",
    stepId: "step-background-1",
    priority: "medium",
    category: "informational",
  },
};

export const mockIdentityVerificationBlock: Block = {
  id: "identity-verification-1",
  type: "verification-completed",
  timestamp: faker.date.recent(),
  actor: mockSystemActor,
  data: {
    verificationType: "identity",
    status: "completed",
    provider: "Veriff Identity Verification",
    details: "Government ID and selfie verification completed successfully",
  },
  metadata: {
    matchId: "match-1",
    stepId: "step-identity-1",
    priority: "low",
    category: "completed",
  },
};

export const mockBankSetupBlock: Block = {
  id: "bank-setup-1",
  type: "step-completed",
  timestamp: faker.date.recent(),
  actor: mockProviderActor,
  data: {
    stepType: "bank_setup",
    stepName: "Bank Account Setup",
    status: "completed",
    progress: 100,
    notes: "Direct deposit information verified and activated",
  },
  metadata: {
    matchId: "match-1",
    stepId: "step-bank-1",
    priority: "low",
    category: "completed",
  },
};

// =====================================================
// COMMUNICATION MOCK DATA
// =====================================================

export const mockMessageBlock: Block = {
  id: "message-1",
  type: "message",
  timestamp: faker.date.recent(),
  actor: mockProviderActor,
  data: {
    content:
      "Hi there! I'm excited about this opportunity. I have a few questions about the schedule and on-call requirements.",
    author: faker.person.fullName(),
    isInternal: false,
  },
  metadata: {
    matchId: "match-1",
    priority: "low",
    category: "informational",
  },
};

export const mockInternalMessageBlock: Block = {
  id: "message-internal-1",
  type: "message",
  timestamp: faker.date.recent(),
  actor: mockOrganizationActor,
  data: {
    content:
      "Internal note: Dr. Johnson has excellent credentials. We should prioritize this match.",
    author: "Metro General Hospital",
    isInternal: true,
  },
  metadata: {
    matchId: "match-1",
    priority: "low",
    category: "informational",
  },
};

// =====================================================
// COLLECTIONS FOR STORIES
// =====================================================

export const mockTimelineBlocks: Block[] = [
  mockRateOfferBlock,
  mockMessageBlock,
  mockRateCounterBlock,
  mockRateAcceptedBlock,
  mockContractCreatedBlock,
  mockContractSignedBlock,
  mockBackgroundCheckBlock,
  mockIdentityVerificationBlock,
  mockBankSetupBlock,
  mockInternalMessageBlock,
];

// State-specific collections
export const mockRateBlocks = {
  offer: mockRateOfferBlock,
  counter: mockRateCounterBlock,
  accepted: mockRateAcceptedBlock,
  declined: mockRateDeclinedBlock,
  expired: mockRateExpiredBlock,
};

export const mockVerificationBlocks = {
  backgroundCheck: mockBackgroundCheckBlock,
  identityVerification: mockIdentityVerificationBlock,
  bankSetup: mockBankSetupBlock,
};

export const mockContractBlocks = {
  created: mockContractCreatedBlock,
  signed: mockContractSignedBlock,
};

export const mockCommunicationBlocks = {
  message: mockMessageBlock,
  internalMessage: mockInternalMessageBlock,
};

// Error states
export const mockErrorBlock: Block = {
  id: "error-1",
  type: "rate-offer",
  timestamp: faker.date.recent(),
  actor: mockSystemActor,
  data: {
    error: "Failed to load block data",
    status: "error",
  },
};

// Loading states (partial data)
export const mockLoadingBlock: Partial<Block> = {
  id: "loading-1",
  type: "rate-offer",
  timestamp: faker.date.recent(),
  actor: undefined,
  data: undefined,
};
