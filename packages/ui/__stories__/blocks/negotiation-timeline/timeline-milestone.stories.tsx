import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs-vite";

import type { Registry } from "@/ui/blocks/negotiation-timeline/timeline-block";
import type { Milestone } from "@/ui/blocks/negotiation-timeline/timeline-milestone";

import { TimelineMilestone } from "@/ui/blocks/negotiation-timeline/timeline-milestone";

import {
  mockRateAcceptedBlock,
  mockRateCounterBlock,
  mockRateDeclinedBlock,
  mockRateOfferBlock,
} from "./data";

// Simple registry for the nested blocks (re-using those from other story)
const mockRegistry: Registry = {
  "rate-offer": {
    type: "rate-offer",
    render: () => null, // placeholder – TimelineBlock will show default renderer
  },
  "rate-counter": { type: "rate-counter", render: () => null },
  "rate-accepted": { type: "rate-accepted", render: () => null },
  "rate-declined": { type: "rate-declined", render: () => null },
};

const mockMilestone: Milestone = {
  id: "ms-1",
  title: "Rate Negotiation",
  status: "active",
  timestamp: new Date(),
  blocks: [
    mockRateOfferBlock,
    mockRateCounterBlock,
    mockRateAccepted<PERSON>lock,
    mockRateDeclinedBlock,
  ],
};

const meta: Meta<typeof TimelineMilestone> = {
  title: "Blocks/Negotiation Timeline/Timeline Milestone",
  component: TimelineMilestone,
  tags: ["autodocs"],
  argTypes: {
    size: {
      options: ["sm", "md", "lg"],
      control: { type: "select" },
    },
    variant: {
      options: ["solid", "outline", "ghost"],
      control: { type: "select" },
    },
    isExpanded: {
      control: { type: "boolean" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    milestone: mockMilestone,
    registry: mockRegistry,
    isExpanded: true,
    size: "md",
    variant: "solid",
    loading: false,
    onToggle: () => {},
  },
};

export const OutlineRTL: Story = {
  args: {
    ...Default.args,
    isExpanded: false,
    variant: "outline",
  },
};

export const SmallOutline: Story = {
  args: {
    ...Default.args,
    size: "sm",
    variant: "outline",
  },
};

export const LargeGhost: Story = {
  args: {
    ...Default.args,
    size: "lg",
    variant: "ghost",
    isExpanded: false,
  },
};
