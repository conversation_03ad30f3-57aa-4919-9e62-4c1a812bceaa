import type { <PERSON>a, StoryObj } from "@storybook/nextjs-vite";

import { RateOfferBlock } from "@/ui/blocks/negotiation-timeline/templates";

import {
  mockErrorBlock,
  mockLoadingBlock,
  mockOrganizationActor,
  mockProviderActor,
  mockRateOfferBlock,
} from "../data";

const meta: Meta<typeof RateOfferBlock> = {
  title: "Blocks/Negotiation Timeline/Templates/Rate Offer Block",
  component: RateOfferBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A timeline block for displaying rate offers with accept/counter actions. Supports pending, accepted, and countered states with conditional action buttons or placeholders.",
      },
    },
  },
  argTypes: {
    data: {
      description: "Rate offer block data",
      control: { type: "object" },
    },
    actor: {
      description: "Block actor information",
      control: { type: "object" },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    data: {
      ...(mockRateOfferBlock.data as any),
      onAccept: () => alert("Rate accepted! 🎉"),
      onCounter: () => alert("Counter offer dialog would open"),
    },
    actor: mockRateOfferBlock.actor,
  },
};

export const FromProvider: Story = {
  args: {
    data: {
      ...(mockRateOfferBlock.data as any),
      message:
        "I'd like to propose this rate based on my experience and the current market.",
      onAccept: () => alert("Rate accepted! 🎉"),
      onCounter: () => alert("Counter offer dialog would open"),
    },
    actor: mockProviderActor,
  },
};

export const WithLongMessage: Story = {
  args: {
    data: {
      ...(mockRateOfferBlock.data as any),
      message:
        "We'd like to offer you this position at our standard rate for emergency medicine physicians. This rate is competitive within our region and includes additional benefits such as professional development opportunities, flexible scheduling, and comprehensive malpractice coverage.",
      onAccept: () => alert("Rate accepted! 🎉"),
      onCounter: () => alert("Counter offer dialog would open"),
    },
    actor: mockRateOfferBlock.actor,
  },
};

export const NoActions: Story = {
  args: {
    data: mockRateOfferBlock.data as any,
    actor: mockRateOfferBlock.actor,
  },
};

export const Loading: Story = {
  args: {
    data: {
      status: "pending" as const,
    },
    loading: true,
    actor: mockRateOfferBlock.actor,
  },
};

export const Error: Story = {
  args: {
    data: {
      rate: 85,
      formattedRate: "$85/hr",
      status: "pending" as const,
      message: "Error loading rate offer data",
    },
    actor: mockErrorBlock.actor,
  },
};
