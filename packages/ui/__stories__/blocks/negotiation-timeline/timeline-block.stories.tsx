import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import type { Registry } from "@/ui/blocks/negotiation-timeline/timeline-block";

import { TimelineBlock } from "@/ui/blocks/negotiation-timeline/timeline-block";

import { mockMessageBlock, mockRateOfferBlock, mockSystemActor } from "./data";

// Create a simple registry for demonstration
const mockRegistry: Registry = {
  "rate-offer": {
    type: "rate-offer",
    render: (block, loading = false) => (
      <div className="flex items-start py-4">
        <div className="flex w-20 shrink-0 items-center justify-center">
          <div className="flex size-12 items-center justify-center rounded-full border-2 border-green-200 bg-green-100">
            <span className="text-xs text-green-600">$</span>
          </div>
        </div>
        <div className="flex-1 pr-4">
          <div className="rounded-lg border bg-card p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-card-foreground">
                Rate Offer
              </span>
              <span className="text-xs text-muted-foreground">
                {block.timestamp.toLocaleTimeString()}
              </span>
            </div>
            <p className="mt-2 text-sm text-muted-foreground">
              {JSON.stringify(block.data)}
            </p>
          </div>
        </div>
      </div>
    ),
  },
  message: {
    type: "message",
    render: (block, loading = false) => (
      <div className="flex items-start py-4">
        <div className="flex w-20 shrink-0 items-center justify-center">
          <div className="flex size-12 items-center justify-center rounded-full border-2 border-blue-200 bg-blue-100">
            <span className="text-xs text-blue-600">💬</span>
          </div>
        </div>
        <div className="flex-1 pr-4">
          <div className="rounded-lg border bg-card p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-card-foreground">
                Message
              </span>
              <span className="text-xs text-muted-foreground">
                {block.timestamp.toLocaleTimeString()}
              </span>
            </div>
            <p className="mt-2 text-sm text-muted-foreground">
              {JSON.stringify(block.data)}
            </p>
          </div>
        </div>
      </div>
    ),
  },
};

const meta: Meta<typeof TimelineBlock> = {
  title: "Blocks/Negotiation Timeline/Timeline Block",
  component: TimelineBlock,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "The core TimelineBlock component that renders different types of timeline blocks using a registry system. This is the foundational component that powers the entire timeline system.",
      },
    },
  },
  argTypes: {
    block: {
      description: "Timeline block data to render",
      control: { type: "object" },
    },
    registry: {
      description: "Registry of block type renderers",
      control: { type: "object" },
    },
    loading: {
      description: "Whether the block is in loading state",
      control: { type: "boolean" },
    },
    isLast: {
      description: "Whether this is the last block in the timeline",
      control: { type: "boolean" },
    },
    size: {
      options: ["small", "medium", "large"],
      control: { type: "select" },
      description: "Size variant for gutter & indicator",
    },
    direction: {
      options: ["ltr", "rtl"],
      control: { type: "radio" },
      description: "Layout direction (LTR/RTL)",
    },
    variant: {
      options: ["solid", "outline", "ghost"],
      control: { type: "select" },
      description: "Card visual style variant",
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const WithRegistry: Story = {
  args: {
    block: mockRateOfferBlock,
    registry: mockRegistry,
    loading: false,
    size: "medium",
    direction: "ltr",
    variant: "solid",
  },
};

export const SmallOutlineRTL: Story = {
  args: {
    block: mockRateOfferBlock,
    registry: mockRegistry,
    size: "small",
    direction: "rtl",
    variant: "outline",
    indicator: <span className="text-xs">$</span>,
  },
};

export const LargeGhostCustomIndicator: Story = {
  args: {
    block: mockMessageBlock,
    registry: mockRegistry,
    size: "large",
    direction: "ltr",
    variant: "ghost",
    indicator: (
      <span role="img" aria-label="Chat" className="text-base">
        💬
      </span>
    ),
  },
};

export const UnknownBlockType: Story = {
  args: {
    block: {
      id: "unknown-1",
      type: "unknown-type",
      timestamp: new Date(),
      actor: mockSystemActor,
      data: {
        message:
          "This is an unknown block type that will use the default renderer",
        someValue: 42,
      },
    },
    registry: mockRegistry,
    loading: false,
  },
};

export const EmptyRegistry: Story = {
  args: {
    block: mockRateOfferBlock,
    registry: {}, // Empty registry to show default renderer
    loading: false,
  },
};

export const Loading: Story = {
  args: {
    block: mockRateOfferBlock,
    registry: mockRegistry,
    loading: true,
  },
};

export const MessageBlock: Story = {
  args: {
    block: mockMessageBlock,
    registry: mockRegistry,
    loading: false,
  },
};

export const SystemBlock: Story = {
  args: {
    block: {
      id: "system-1",
      type: "system-notification",
      timestamp: new Date(),
      actor: mockSystemActor,
      data: {
        message: "System generated notification",
        level: "info",
        automated: true,
      },
      metadata: {
        priority: "low",
        category: "informational",
      },
    },
    registry: {}, // Will use default renderer
    loading: false,
  },
};

export const ComplexData: Story = {
  args: {
    block: {
      id: "complex-1",
      type: "complex-data",
      timestamp: new Date(),
      actor: mockSystemActor, // Changed from mockOrganizationActor to mockSystemActor
      data: {
        nested: {
          values: [1, 2, 3],
          object: {
            a: "test",
            b: true,
            c: null,
          },
        },
        array: ["item1", "item2"],
        boolean: true,
        number: 123.45,
      },
      metadata: {
        priority: "high",
        category: "action_required",
        customField: "custom value",
      },
    },
    registry: {}, // Will use default renderer to show JSON
    loading: false,
  },
};

export const WithoutData: Story = {
  args: {
    block: {
      id: "no-data-1",
      type: "empty-block",
      timestamp: new Date(),
      actor: mockSystemActor, // Changed from mockProviderActor to mockSystemActor
      data: null,
    },
    registry: {},
    loading: false,
  },
};

export const LoadingUnknownType: Story = {
  args: {
    block: {
      id: "loading-unknown-1",
      type: "unknown-loading-type",
      timestamp: new Date(),
      actor: mockSystemActor,
      data: { placeholder: "data" },
    },
    registry: {},
    loading: true,
  },
};
