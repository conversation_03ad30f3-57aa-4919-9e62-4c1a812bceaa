import { z } from "zod";

// Zod schemas for each metadata type
export const zRateNegotiationMetadata = z.object({
  currentOfferRate: z.number(),
  lastOfferBy: z.enum(["PROVIDER", "ORGANIZATION"]),
  lastOfferAt: z.string(),
  offerExpiresAt: z.string().optional(),
  offerHistory: z.array(
    z.object({
      id: z.string(),
      proposedRate: z.number(),
      madeBy: z.enum(["PROVIDER", "ORGANIZATION"]),
      madeAt: z.string(),
      message: z.string().optional(),
      expiresAt: z.string().optional(),
      status: z.enum(["PENDING", "ACCEPTED", "DECLINED", "EXPIRED", "WITHDRAWN"]),
      declineReason: z.string().optional(),
      respondedAt: z.string().optional(),
    })
  ),
  strategy: z.enum(["conservative", "competitive", "balanced", "premium"]),
  allowCounterOffers: z.boolean(),
  maxNegotiationRounds: z.number(),
  currentRound: z.number(),
  threadId: z.string().optional(),
  compensationId: z.string(),
});

export const zIdentityVerificationMetadata = z.object({
  verificationType: z.enum(["GOVERNMENT_ID", "PASSPORT", "DRIVER_LICENSE"]),
  documentNumber: z.string().optional(),
  documentType: z.string().optional(),
  verificationStatus: z.enum(["PENDING", "IN_PROGRESS", "VERIFIED", "REJECTED"]),
  serviceProvider: z.enum(["JUMIO", "ONFIDO", "ID_ME", "MANUAL"]).optional(),
  externalReferenceId: z.string().optional(),
  confidence: z.number().optional(),
  rejectionReason: z.string().optional(),
  verifiedAt: z.string().optional(),
  documentImages: z
    .object({
      front: z.string().optional(),
      back: z.string().optional(),
      selfie: z.string().optional(),
    })
    .optional(),
});

export const zBackgroundCheckMetadata = z.object({
  checkTypes: z.array(
    z.enum(["CRIMINAL", "EMPLOYMENT", "EDUCATION", "REFERENCE", "CREDIT"])
  ),
  scope: z.enum(["NATIONAL", "STATE", "COUNTY", "INTERNATIONAL"]),
  serviceProvider: z.enum(["CHECKR", "STERLING", "HireRight", "ACCURATE", "MANUAL"]).optional(),
  externalReferenceId: z.string().optional(),
  packageId: z.string().optional(),
  status: z.enum(["PENDING", "IN_PROGRESS", "COMPLETED", "ADVERSE_ACTION", "DISPUTED"]),
  completedChecks: z.array(z.string()),
  pendingChecks: z.array(z.string()),
  overallResult: z.enum(["CLEAR", "CONSIDER", "SUSPENDED"]).optional(),
  findings: z
    .array(
      z.object({
        type: z.string(),
        severity: z.enum(["LOW", "MEDIUM", "HIGH"]),
        description: z.string(),
        county: z.string().optional(),
        date: z.string().optional(),
      })
    )
    .optional(),
  initiatedAt: z.string().optional(),
  estimatedCompletionAt: z.string().optional(),
  completedAt: z.string().optional(),
});

export const zInterviewMetadata = z.object({
  scheduledAt: z.string().optional(),
  duration: z.number().optional(),
  timezone: z.string().optional(),
  type: z.enum(["PHONE", "VIDEO", "IN_PERSON", "PANEL"]),
  platform: z.enum(["ZOOM", "TEAMS", "GOOGLE_MEET", "CUSTOM"]).optional(),
  meetingLink: z.string().optional(),
  meetingId: z.string().optional(),
  interviewers: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      role: z.string(),
      email: z.string().optional(),
    })
  ),
  questions: z
    .array(
      z.object({
        id: z.string(),
        question: z.string(),
        category: z.enum(["TECHNICAL", "BEHAVIORAL", "SITUATIONAL", "CULTURAL"]),
        response: z.string().optional(),
        rating: z.number().optional(),
        notes: z.string().optional(),
      })
    )
    .optional(),
  overallRating: z.number().optional(),
  recommendation: z.enum(["HIRE", "NO_HIRE", "MAYBE", "PENDING"]),
  feedback: z.string().optional(),
  strengths: z.array(z.string()).optional(),
  concerns: z.array(z.string()).optional(),
});

export const zContractTermsMetadata = z.object({
  contractType: z.enum(["W2", "1099", "CORP_TO_CORP", "TEMP_TO_PERM"]),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  renewalTerms: z.string().optional(),
  workLocation: z.enum(["ON_SITE", "REMOTE", "HYBRID"]),
  workSchedule: z
    .object({
      hoursPerWeek: z.number(),
      daysPerWeek: z.number(),
      shiftType: z.enum(["DAYS", "EVENINGS", "NIGHTS", "ROTATING"]),
    })
    .optional(),
  compensationId: z.string(),
  additionalBenefits: z.array(z.string()).optional(),
  negotiableTerms: z.array(z.string()),
  agreedTerms: z.record(z.unknown()),
  pendingTerms: z.record(z.unknown()),
  governingLaw: z.string().optional(),
  disputeResolution: z.string().optional(),
  terminationClause: z.string().optional(),
  contractTemplateId: z.string().optional(),
  draftVersions: z.array(
    z.object({
      version: z.number(),
      createdAt: z.string(),
      createdBy: z.string(),
      changes: z.array(z.string()),
    })
  ),
});

export const zScheduleNegotiationMetadata = z.object({
  preferredStartDate: z.string().optional(),
  availableDays: z.array(
    z.enum([
      "MONDAY",
      "TUESDAY",
      "WEDNESDAY",
      "THURSDAY",
      "FRIDAY",
      "SATURDAY",
      "SUNDAY",
    ])
  ),
  preferredShifts: z.array(
    z.enum(["MORNING", "AFTERNOON", "EVENING", "NIGHT"])
  ),
  isFlexible: z.boolean(),
  blackoutDates: z.array(z.string()).optional(),
  vacationRequests: z
    .array(
      z.object({
        startDate: z.string(),
        endDate: z.string(),
        reason: z.string().optional(),
        approved: z.boolean().optional(),
      })
    )
    .optional(),
  proposedSchedules: z.array(
    z.object({
      id: z.string(),
      proposedBy: z.enum(["PROVIDER", "ORGANIZATION"]),
      schedule: z.object({
        startDate: z.string(),
        pattern: z.enum(["WEEKLY", "BIWEEKLY", "MONTHLY", "CUSTOM"]),
        shifts: z.array(
          z.object({
            day: z.string(),
            startTime: z.string(),
            endTime: z.string(),
            break: z.number().optional(),
          })
        ),
      }),
      status: z.enum(["PENDING", "ACCEPTED", "REJECTED", "COUNTERED"]),
      proposedAt: z.string(),
      respondedAt: z.string().optional(),
    })
  ),
  agreedSchedule: z
    .object({
      startDate: z.string(),
      pattern: z.string(),
      shifts: z.array(z.unknown()),
    })
    .optional(),
});

export const zJobPostMetadata = z.object({
  matchSteps: z
    .array(
      z.enum([
        "IDENTITY_VERIFICATION",
        "BACKGROUND_CHECK",
        "RATE_NEGOTIATION",
        "CONTRACT_TERMS",
      ])
    )
    .optional(),
  emrSystem: z.enum(["EPIC", "Cerner", "Meditech", "Allscripts", "Other"]).optional(),
  requiredDocuments: z
    .array(
      z.enum([
        "BLS",
        "ACLS",
        "PALS",
        "TB_TEST",
        "COVID_VAX",
        "RN_LICENSE",
        "OTHER",
      ])
    )
    .optional(),
  requiredLanguages: z
    .array(z.enum(["en", "es", "zh", "fr", "vi", "tl", "other"]))
    .optional(),
  requiredSpecialties: z
    .array(
      z.enum([
        "ER",
        "ICU",
        "NICU",
        "L&D",
        "Telemetry",
        "Psych",
        "OR",
        "Radiology",
        "Cardiology",
        "Oncology",
        "General",
      ])
    )
    .optional(),
  extraCertifications: z
    .array(z.enum(["DEA", "BoardCertified", "BoardEligible"]))
    .optional(),
  orientation: z
    .object({
      isPaid: z.boolean(),
      durationHours: z.number(),
    })
    .optional(),
  perks: z
    .object({
      travelReimbursement: z.string().optional(),
      housingDescription: z.string().optional(),
      parkingInfo: z.string().optional(),
      uniformRequirement: z.string().optional(),
    })
    .optional(),
  promotionTags: z
    .array(z.enum(["urgent", "featured", "internal_only"]))
    .optional(),
  highlightColor: z.string().optional(),
});

export const zJobMetadataUnion = z.union([
  zJobPostMetadata,
  zRateNegotiationMetadata,
  zIdentityVerificationMetadata,
  zBackgroundCheckMetadata,
  zInterviewMetadata,
  zContractTermsMetadata,
  zScheduleNegotiationMetadata,
]);
