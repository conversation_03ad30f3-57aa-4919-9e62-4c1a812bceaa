import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Prisma } from "@axa/database-medical";
import { MatchInitiator, MatchStatus } from "@axa/database-medical";

import { ActionType, ResourceType } from "../../../constants/actions";
import { performAction } from "../../../lib/actions";
import {
  authorizedProcedure,
  createTRPCRouter,
  protectedProcedure,
} from "../../../trpc";

export const matchActionsRouter = createTRPCRouter({
  // Accept a match (for organization-initiated matches by providers, or provider-initiated matches by organizations)
  accept: authorizedProcedure
    .input(
      z.object({
        id: z.string(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get match and validate
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          status: true,
          initiator: true,
          providerId: true,
          organizationId: true,
          jobId: true,
          provider: {
            select: {
              person: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          organization: {
            select: {
              name: true,
            },
          },
          job: {
            select: {
              role: true,
              summary: true,
            },
          },
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      if (match.status !== MatchStatus.PENDING) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Match is not in pending status",
        });
      }

      // Determine if user can accept this match
      const isProvider = ctx.provider?.id === match.providerId;
      const isOrganization = ctx.organization?.id === match.organizationId;

      if (!isProvider && !isOrganization) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to accept this match",
        });
      }

      // Validate acceptance logic
      if (isProvider && match.initiator !== MatchInitiator.ORGANIZATION) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Providers can only accept organization-initiated matches",
        });
      }

      if (isOrganization && match.initiator !== MatchInitiator.PROVIDER) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organizations can only accept provider-initiated matches",
        });
      }

      // Update match status
      const updatedMatch = await ctx.prisma.match.update({
        where: { id: input.id },
        data: { status: MatchStatus.ACCEPTED },
        select: { id: true },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.ACCEPT_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: match.organizationId,
        providerId: match.providerId,
        metadata: {
          matchId: match.id,
          jobId: match.jobId,
          message: input.message,
          acceptedBy: isProvider ? "PROVIDER" : "ORGANIZATION",
        },
      });

      return updatedMatch;
    }),

  // Decline a match
  decline: authorizedProcedure
    .input(
      z.object({
        id: z.string(),
        reason: z.string().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get match and validate
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          status: true,
          providerId: true,
          organizationId: true,
          jobId: true,
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      if (match.status !== MatchStatus.PENDING) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Match is not in pending status",
        });
      }

      // Determine if user can decline this match
      const isProvider = ctx.provider?.id === match.providerId;
      const isOrganization = ctx.organization?.id === match.organizationId;

      if (!isProvider && !isOrganization) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to decline this match",
        });
      }

      // Update match status
      const updatedMatch = await ctx.prisma.match.update({
        where: { id: input.id },
        data: { status: MatchStatus.DECLINED },
        select: { id: true },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.DECLINE_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: match.organizationId,
        providerId: match.providerId,
        metadata: {
          matchId: match.id,
          jobId: match.jobId,
          reason: input.reason,
          message: input.message,
          declinedBy: isProvider ? "PROVIDER" : "ORGANIZATION",
        },
      });

      return updatedMatch;
    }),

  // Withdraw a match (for the party that initiated it)
  withdraw: authorizedProcedure
    .input(
      z.object({
        id: z.string(),
        reason: z.string().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get match and validate
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          status: true,
          initiator: true,
          providerId: true,
          organizationId: true,
          jobId: true,
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      if (match.status !== MatchStatus.PENDING) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Only pending matches can be withdrawn",
        });
      }

      // Determine if user can withdraw this match (only the initiator can withdraw)
      const isProvider = ctx.user.providerId === match.providerId;
      const isOrganization = ctx.user.organizationId === match.organizationId;

      if (!isProvider && !isOrganization) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to withdraw this match",
        });
      }

      // Validate withdrawal logic - only initiator can withdraw
      if (isProvider && match.initiator !== MatchInitiator.PROVIDER) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Providers can only withdraw matches they initiated",
        });
      }

      if (isOrganization && match.initiator !== MatchInitiator.ORGANIZATION) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organizations can only withdraw matches they initiated",
        });
      }

      // Update match status
      const updatedMatch = await ctx.prisma.match.update({
        where: { id: input.id },
        data: { status: MatchStatus.WITHDRAWN },
        select: { id: true },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.WITHDRAW_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: match.organizationId,
        providerId: match.providerId,
        metadata: {
          matchId: match.id,
          jobId: match.jobId,
          reason: input.reason,
          message: input.message,
          withdrawnBy: isProvider ? "PROVIDER" : "ORGANIZATION",
        },
      });

      return updatedMatch;
    }),

  // Apply to a job (create provider-initiated match)
  apply: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        message: z.string().optional(),
        proposedRate: z.number().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Get job and validate
      const job = await ctx.prisma.jobPost.findUnique({
        where: { id: input.jobId },
        select: {
          id: true,
          organizationId: true,
          status: true,
          allowRateNegotiation: true,
          minNegotiableRate: true,
          maxNegotiableRate: true,
        },
      });

      if (!job) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Check if provider already has a match for this job
      const existingMatch = await ctx.prisma.match.findFirst({
        where: {
          jobId: input.jobId,
          providerId: provider.id,
          status: { not: MatchStatus.WITHDRAWN },
        },
      });

      if (existingMatch) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "You already have an active match for this job",
        });
      }

      // Validate proposed rate if provided
      if (input.proposedRate) {
        if (!job.allowRateNegotiation) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Rate negotiation is not allowed for this job",
          });
        }

        if (
          job.minNegotiableRate &&
          input.proposedRate < job.minNegotiableRate
        ) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Proposed rate must be at least $${job.minNegotiableRate}`,
          });
        }

        if (
          job.maxNegotiableRate &&
          input.proposedRate > job.maxNegotiableRate
        ) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Proposed rate cannot exceed $${job.maxNegotiableRate}`,
          });
        }
      }

      // Create match
      const match = await ctx.prisma.match.create({
        data: {
          jobId: input.jobId,
          providerId: provider.id,
          organizationId: job.organizationId,
          status: MatchStatus.PENDING,
          initiator: MatchInitiator.PROVIDER,
          initiationNote: input.message,
          includeCompensation: !!input.proposedRate,
        },
        select: { id: true },
      });

      // Create compensation record if rate is proposed
      if (input.proposedRate) {
        await ctx.prisma.jobCompensation.create({
          data: {
            matchId: match.id,
            currentOfferRate: input.proposedRate,
            negotiationStatus: "ACTIVE",
            negotiationCount: 1,
          },
        });
      }

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.CREATE_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: job.organizationId,
        providerId: provider.id,
        metadata: {
          matchId: match.id,
          jobId: input.jobId,
          message: input.message,
          proposedRate: input.proposedRate,
        },
      });

      return match;
    }),
});
