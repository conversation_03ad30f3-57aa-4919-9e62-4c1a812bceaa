import { mergeRouters } from "@trpc/server/unstable-core-do-not-import";

import { createTRPCRouter } from "../../../trpc";
import { matchActionsRouter } from "./actions";
import { contractsRouter } from "./contracts";
import { matchesRouter } from "./matches";
import { rateNegotiationRouter } from "./rates";
import { matchStepsRouter } from "./steps";
import { timelineRouter } from "./timeline";

export const matchesRouterBundle = mergeRouters(
  matchesRouter,
  createTRPCRouter({
    actions: matchActionsRouter,
    steps: matchStepsRouter,
    rates: rateNegotiationRouter,
    contracts: contractsRouter,
    timeline: timelineRouter,
  }),
);
