import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Prisma } from "@axa/database-medical";
import { MatchInitiator, MatchStatus } from "@axa/database-medical";
import { calculateSkip } from "@axa/lib/utils";

import type { ProcedureResult } from "../../types/select";

import { ActionType, ResourceType } from "../../constants/actions";
import { performAction } from "../../lib/actions";
import { createTRPCRouter, protectedProcedure } from "../../trpc";

const zMatchStatus = z.nativeEnum(MatchStatus);
const zMatchInitiator = z.nativeEnum(MatchInitiator);

// Include schema for provider matches
const zIncludeSchema = z.object({
  job: z.boolean().optional(),
  organization: z.boolean().optional(),
  compensation: z.boolean().optional(),
  steps: z.boolean().optional(),
  actions: z.boolean().optional(),
  thread: z.boolean().optional(),
});

// Selection objects for different includes
const selection = {
  match: {
    id: true,
    status: true,
    initiator: true,
    createdAt: true,
    updatedAt: true,
    initiationNote: true,
    providerId: true,
    organizationId: true,
    jobId: true,
  },
  job: {
    id: true,
    summary: true,
    role: true,
    status: true,
    paymentType: true,
    paymentRate: true,
    location: {
      select: {
        id: true,
        name: true,
        address: {
          select: {
            formatted: true,
          },
        },
      },
    },
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  compensation: {
    id: true,
    finalAgreedRate: true,
    currentOfferRate: true,
    minRate: true,
    maxRate: true,
    negotiationStatus: true,
    negotiationCount: true,
    offerExpiresAt: true,
  },
  steps: {
    id: true,
    type: true,
    status: true,
    order: true,
    isRequired: true,
    isSkippable: true,
    metadata: true,
    createdAt: true,
    updatedAt: true,
  },
  actions: {
    id: true,
    type: true,
    createdAt: true,
    metadata: true,
    actor: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        avatar: true,
      },
    },
  },
  thread: {
    id: true,
    messages: {
      select: {
        id: true,
        content: true,
        createdAt: true,
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc" as const,
      },
      take: 10, // Limit to recent messages
    },
  },
} satisfies {
  match: Prisma.MatchSelect;
  job: Prisma.JobPostSelect;
  organization: Prisma.OrganizationSelect;
  compensation: Prisma.JobCompensationSelect;
  steps: Prisma.MatchStepSelect;
  actions: Prisma.ActionSelect;
  thread: Prisma.ThreadSelect;
};

export const providerMatchesRouter = createTRPCRouter({
  // List matches for the current provider
  list: protectedProcedure
    .input(
      z.object({
        query: z.string().optional(),
        pageSize: z.number().optional().default(10),
        pageNumber: z.number().optional().default(0),
        status: zMatchStatus.optional(),
        initiator: zMatchInitiator.optional(),
        include: zIncludeSchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Build where clause
      const where: Prisma.MatchWhereInput = {
        providerId: provider.id,
        ...(input.status && { status: input.status }),
        ...(input.initiator && { initiator: input.initiator }),
        ...(input.query && {
          OR: [
            {
              job: {
                summary: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
            },
            {
              job: {
                role: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
            },
            {
              organization: {
                name: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
            },
          ],
        }),
      };

      // Build select clause
      const select = {
        ...selection.match,
        job: input.include?.job ? { select: selection.job } : undefined,
        organization: input.include?.organization
          ? { select: selection.organization }
          : undefined,
        compensation: input.include?.compensation
          ? { select: selection.compensation }
          : undefined,
        steps: input.include?.steps
          ? { select: selection.steps, orderBy: { order: "asc" } }
          : undefined,
        actions: input.include?.actions
          ? { select: selection.actions, orderBy: { createdAt: "desc" } }
          : undefined,
        thread: input.include?.thread
          ? { select: selection.thread }
          : undefined,
      } satisfies Prisma.MatchSelect;

      // Execute queries
      const [matches, total] = await Promise.all([
        ctx.prisma.match.findMany({
          where,
          select,
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          orderBy: {
            createdAt: "desc",
          },
        }),
        ctx.prisma.match.count({ where }),
      ]);

      return {
        items: matches as unknown as ProcedureResult<
          typeof select,
          Prisma.$MatchPayload
        >[],
        total,
      };
    }),

  // Get a specific match for the current provider
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        include: zIncludeSchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Build select clause
      const select = {
        ...selection.match,
        job: input.include?.job ? { select: selection.job } : undefined,
        organization: input.include?.organization
          ? { select: selection.organization }
          : undefined,
        compensation: input.include?.compensation
          ? { select: selection.compensation }
          : undefined,
        steps: input.include?.steps
          ? { select: selection.steps, orderBy: { order: "asc" } }
          : undefined,
        actions: input.include?.actions
          ? { select: selection.actions, orderBy: { createdAt: "desc" } }
          : undefined,
        thread: input.include?.thread
          ? { select: selection.thread }
          : undefined,
      } satisfies Prisma.MatchSelect;

      const match = await ctx.prisma.match.findFirst({
        where: {
          id: input.id,
          providerId: provider.id, // Ensure provider can only access their own matches
        },
        select,
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      return match as unknown as ProcedureResult<
        typeof select,
        Prisma.$MatchPayload
      >;
    }),

  // Accept a match (for organization-initiated matches)
  accept: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Get match and validate
      const match = await ctx.prisma.match.findFirst({
        where: {
          id: input.id,
          providerId: provider.id,
          status: MatchStatus.PENDING,
          initiator: MatchInitiator.ORGANIZATION, // Only accept org-initiated matches
        },
        select: {
          id: true,
          organizationId: true,
          jobId: true,
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found or cannot be accepted",
        });
      }

      // Update match status
      const updatedMatch = await ctx.prisma.match.update({
        where: { id: input.id },
        data: { status: MatchStatus.ACCEPTED },
        select: { id: true },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.ACCEPT_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: match.organizationId,
        providerId: provider.id,
        metadata: {
          matchId: match.id,
          jobId: match.jobId,
          message: input.message,
        },
      });

      return updatedMatch;
    }),

  // Decline a match
  decline: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        reason: z.string().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Get match and validate
      const match = await ctx.prisma.match.findFirst({
        where: {
          id: input.id,
          providerId: provider.id,
          status: MatchStatus.PENDING,
        },
        select: {
          id: true,
          organizationId: true,
          jobId: true,
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found or cannot be declined",
        });
      }

      // Update match status
      const updatedMatch = await ctx.prisma.match.update({
        where: { id: input.id },
        data: { status: MatchStatus.DECLINED },
        select: { id: true },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.DECLINE_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: match.organizationId,
        providerId: provider.id,
        metadata: {
          matchId: match.id,
          jobId: match.jobId,
          reason: input.reason,
          message: input.message,
        },
      });

      return updatedMatch;
    }),

  // Withdraw a match (for provider-initiated matches)
  withdraw: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        reason: z.string().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Get match and validate
      const match = await ctx.prisma.match.findFirst({
        where: {
          id: input.id,
          providerId: provider.id,
          status: MatchStatus.PENDING,
          initiator: MatchInitiator.PROVIDER, // Only withdraw provider-initiated matches
        },
        select: {
          id: true,
          organizationId: true,
          jobId: true,
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found or cannot be withdrawn",
        });
      }

      // Update match status
      const updatedMatch = await ctx.prisma.match.update({
        where: { id: input.id },
        data: { status: MatchStatus.WITHDRAWN },
        select: { id: true },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.WITHDRAW_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: match.organizationId,
        providerId: provider.id,
        metadata: {
          matchId: match.id,
          jobId: match.jobId,
          reason: input.reason,
          message: input.message,
        },
      });

      return updatedMatch;
    }),

  // Apply to a job (create provider-initiated match)
  apply: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        message: z.string().optional(),
        proposedRate: z.number().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Get job and validate
      const job = await ctx.prisma.jobPost.findUnique({
        where: { id: input.jobId },
        select: {
          id: true,
          organizationId: true,
          status: true,
          allowRateNegotiation: true,
          minNegotiableRate: true,
          maxNegotiableRate: true,
        },
      });

      if (!job) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Check if provider already has a match for this job
      const existingMatch = await ctx.prisma.match.findFirst({
        where: {
          jobId: input.jobId,
          providerId: provider.id,
        },
      });

      if (existingMatch) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "You already have a match for this job",
        });
      }

      // Validate proposed rate if provided
      if (input.proposedRate) {
        if (!job.allowRateNegotiation) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Rate negotiation is not allowed for this job",
          });
        }

        if (
          job.minNegotiableRate &&
          input.proposedRate < job.minNegotiableRate
        ) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Proposed rate must be at least $${job.minNegotiableRate}`,
          });
        }

        if (
          job.maxNegotiableRate &&
          input.proposedRate > job.maxNegotiableRate
        ) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Proposed rate cannot exceed $${job.maxNegotiableRate}`,
          });
        }
      }

      // Create match
      const match = await ctx.prisma.match.create({
        data: {
          jobId: input.jobId,
          providerId: provider.id,
          organizationId: job.organizationId,
          status: MatchStatus.PENDING,
          initiator: MatchInitiator.PROVIDER,
          initiationNote: input.message,
          includeCompensation: !!input.proposedRate,
        },
        select: { id: true },
      });

      // Create compensation record if rate is proposed
      if (input.proposedRate) {
        await ctx.prisma.jobCompensation.create({
          data: {
            matchId: match.id,
            currentOfferRate: input.proposedRate,
            negotiationStatus: "ACTIVE",
            negotiationCount: 1,
          },
        });
      }

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.CREATE_MATCH,
        resourceType: ResourceType.MATCH,
        resourceId: match.id,
        organizationId: job.organizationId,
        providerId: provider.id,
        metadata: {
          matchId: match.id,
          jobId: input.jobId,
          message: input.message,
          proposedRate: input.proposedRate,
        },
      });

      return match;
    }),

  // Negotiate rate (propose new rate)
  negotiate: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        proposedRate: z.number().positive(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get current provider
      const provider = await ctx.prisma.provider.findUnique({
        where: { personId: ctx.user.id },
        select: { id: true },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider profile not found",
        });
      }

      // Get match and validate
      const match = await ctx.prisma.match.findFirst({
        where: {
          id: input.id,
          providerId: provider.id,
          status: {
            in: [MatchStatus.PENDING, MatchStatus.NEGOTIATING],
          },
        },
        select: {
          id: true,
          organizationId: true,
          jobId: true,
          job: {
            select: {
              allowRateNegotiation: true,
              minNegotiableRate: true,
              maxNegotiableRate: true,
            },
          },
          compensation: {
            select: {
              id: true,
              negotiationCount: true,
            },
          },
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found or cannot be negotiated",
        });
      }

      if (!match.job.allowRateNegotiation) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Rate negotiation is not allowed for this job",
        });
      }

      // Validate proposed rate
      if (
        match.job.minNegotiableRate &&
        input.proposedRate < match.job.minNegotiableRate
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Proposed rate must be at least $${match.job.minNegotiableRate}`,
        });
      }

      if (
        match.job.maxNegotiableRate &&
        input.proposedRate > match.job.maxNegotiableRate
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Proposed rate cannot exceed $${match.job.maxNegotiableRate}`,
        });
      }

      // Update or create compensation
      if (match.compensation) {
        await ctx.prisma.jobCompensation.update({
          where: { id: match.compensation.id },
          data: {
            currentOfferRate: input.proposedRate,
            negotiationStatus: "ACTIVE",
            negotiationCount: match.compensation.negotiationCount + 1,
            offerExpiresAt: new Date(Date.now() + 48 * 60 * 60 * 1000), // 48 hours
          },
        });
      } else {
        await ctx.prisma.jobCompensation.create({
          data: {
            matchId: match.id,
            currentOfferRate: input.proposedRate,
            negotiationStatus: "ACTIVE",
            negotiationCount: 1,
            offerExpiresAt: new Date(Date.now() + 48 * 60 * 60 * 1000), // 48 hours
          },
        });
      }

      // Update match status to negotiating
      await ctx.prisma.match.update({
        where: { id: input.id },
        data: { status: MatchStatus.NEGOTIATING },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.SUBMIT_RATE_OFFER,
        resourceType: ResourceType.JOB_COMPENSATION,
        resourceId: match.compensation?.id || match.id,
        organizationId: match.organizationId,
        providerId: provider.id,
        metadata: {
          matchId: match.id,
          jobId: match.jobId,
          proposedRate: input.proposedRate,
          message: input.message,
        },
      });

      return { success: true };
    }),
});
