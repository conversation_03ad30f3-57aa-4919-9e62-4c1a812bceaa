import { mergeRouters } from "@trpc/server/unstable-core-do-not-import";

import { createTRPCRouter } from "../../trpc";
import { providersAnalyticsRouter } from "./analytics";
import { providerMatchesRouter } from "./matches";
import { providersOnboardingRouter } from "./onboarding";
import { providerProspectingRouter } from "./prospecting";
import { providerSearchRouter } from "./provider-search";
import { providersRouter as providers } from "./providers";
import { providersVerificationRouter } from "./verification";

export { jobExperiencesRouter } from "./jobExperience";
export { qualificationsRouter } from "./qualifications";

export const providersRouter = mergeRouters(
  providers,
  providerSearchRouter,
  createTRPCRouter({
    analytics: providersAnalyticsRouter,
    onboarding: providersOnboardingRouter,
    verification: providersVerificationRouter,
    prospecting: providerProspectingRouter,
  }),
);
