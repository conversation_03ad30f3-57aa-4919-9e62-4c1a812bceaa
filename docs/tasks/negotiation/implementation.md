# Matches System Implementation Guide

## Overview

The Matches system replaces the previous applications/offers workflow with a unified approach for connecting providers and job opportunities. This system enables rate negotiation, status tracking, and streamlined communication between organizations and providers.

## Architecture

### Core Components

1. **Matches API** - Backend API for match management
2. **Prospecting Widget** - Organization tool for finding and matching providers
3. **Match Tables** - List views for managing matches
4. **Match Detail Pages** - Individual match management
5. **Provider Match Interface** - Provider-side match management

### Data Flow

```
Organization Job → Prospecting Widget → Provider Search → Create Match
                                                              ↓
Provider Dashboard ← Match Notification ← Match Created → Organization Dashboard
                                                              ↓
Provider Actions → Accept/Decline/Negotiate → Match Status Updates → Organization Notifications
```

## Implementation Details

### 1. Prospecting Widget System

**Location**: `apps/web-med/src/widgets/prospecting/`

Enables organizations to find and match with providers. Replaces the old offer system with match creation.

**Key Components**: ProspectingContext (data layer), provider search/cards, match creation actions.

**Usage**: Integrated into job pages for provider discovery and matching.

### 2. Match Tables System

**Location**: `apps/web-med/src/components/tables/ListMatches.tsx`

Provides tabular views of matches with filtering, sorting, and pagination.

#### Features

- **Status Filtering**: Filter by match status (pending, negotiating, matched, etc.)
- **Initiator Filtering**: Filter by who initiated the match
- **Search**: Text search across match data
- **Export**: CSV export functionality
- **Pagination**: Standard pagination controls

#### Columns

- **Status**: Color-coded status badges
- **Job**: Job title with role information
- **Provider**: Provider name with title
- **Organization**: Organization name (clickable)
- **Rate**: Current rate with negotiation status
- **Initiator**: Who initiated the match
- **Created**: Time since creation

#### Usage

```tsx
import ListMatches from "@/components/tables/ListMatches";
import { useListMatches } from "@/hooks/lists/use-list-matches";

const { data, loading, error } = useListMatches({
  group: "match",
  defaultPageSize: 10,
});

<ListMatches matches={data} loading={loading} />;
```

### 3. Match Detail Pages

**Location**: `apps/web-med/src/www/core/match/`

Individual match management interface with detailed information and actions.

#### Features

- **Match Overview**: Status, initiator, creation date
- **Job Details**: Job information with link to full details
- **Provider Details**: Provider information with profile link
- **Organization Details**: Organization information
- **Rate Details**: Current rates, negotiation status, agreed rates

#### Navigation

```
/app/matches              → List all matches
/app/matches/[id]         → View individual match details
```

### 4. API Integration

**Endpoints Used**:

- `api.jobs.matches.list` - List matches for jobs
- `api.jobs.matches.get` - Get individual match details
- `api.jobs.matches.create` - Create new matches
- `api.providers.prospecting.search` - Search providers for matching

#### Match Creation Flow

```tsx
const createMatch = async (providerId: string, message?: string) => {
  const match = await api.jobs.matches.create.mutateAsync({
    jobId: job.id,
    providerId,
    initiator: "ORGANIZATION",
    initiationNote: message,
    includeCompensation: true,
  });

  // Refresh matches after creation
  await refetchMatches();
  return match;
};
```

### 5. Integration Points

#### Job Page Integration

**Location**: `apps/web-med/src/www/organizations/job/Job.tsx`

The prospecting widget is integrated into job pages:

```tsx
import { ProspectingWidget } from "@/widgets/prospecting";

// In Job component
<ProspectingWidget
  jobId={props.job.data?.id ?? ""}
  onViewJobDetails={() => {
    // Job details are already visible on this page
  }}
  onMatchCreated={(match) => {
    console.log("Match created:", match);
  }}
/>;
```

#### Navigation Integration

**Location**: `apps/web-med/src/components/layouts/OrganizationLayout.tsx`

Matches appear in the main navigation between "Offers" and "Contracts":

```tsx
{
  href: "/app/matches",
  label: "Matches",
  icon: UserCheckIcon,
  badge: null,
}
```

## Status Tracking

### Match Statuses

- **PENDING**: Initial state when match is created
- **ACCEPTED**: Provider has accepted the match
- **DECLINED**: Provider has declined the match
- **VALIDATING**: Background checks and validation in progress
- **NEGOTIATING**: Rate negotiation in progress
- **FINALIZING**: Final contract terms being agreed
- **MATCHED**: Successfully matched and ready for contract
- **WITHDRAWN**: Match withdrawn by either party
- **CANCELLED**: Match cancelled by system/admin
- **EXPIRED**: Match expired due to timeout

### Initiator Types

- **PROVIDER**: Provider initiated the match (applied)
- **ORGANIZATION**: Organization initiated the match (invited)
- **MUTUAL**: Both parties expressed interest
- **REFERRAL**: Match created through referral system

## Next Steps

### Provider-Side Implementation

1. **Provider Match Dashboard**: View all matches from provider perspective
2. **Match Actions**: Accept, decline, negotiate matches
3. **Match Notifications**: Real-time notifications for new matches
4. **Rate Negotiation**: Provider-initiated rate negotiations

### API Enhancements

1. **Provider Actions**: Accept/decline match endpoints
2. **Negotiation Flow**: Rate negotiation workflow
3. **Notifications**: Real-time match notifications
4. **Status Transitions**: Automated status updates

### Integration Requirements

1. **Contract Integration**: Automatic contract creation for matched items
2. **Notification System**: Email/SMS notifications for match events
3. **Analytics**: Match success rates and performance metrics
4. **Reporting**: Match pipeline and conversion reporting

## Migration Notes

### From Applications/Offers to Matches

- **Applications** → **Provider-initiated Matches**
- **Offers** → **Organization-initiated Matches**
- **Accept Application** → **Accept Match**
- **Send Offer** → **Create Match**
- **Withdraw Offer** → **Withdraw Match**

### Data Migration

Existing applications and offers should be migrated to matches with appropriate status mapping and historical data preservation.

## Testing Strategy

1. **Unit Tests**: Individual component testing
2. **Integration Tests**: API integration testing
3. **E2E Tests**: Complete match workflow testing
4. **Performance Tests**: Search and pagination performance
5. **User Acceptance Tests**: Organization and provider workflows

## Storybook Documentation

### Components with Stories

- **ListMatches**: `apps/web-med/__stories__/components/tables/ListMatches.stories.tsx`
- **Matches Page**: `apps/web-med/__stories__/pages/core/matches/Matches.stories.tsx`

### Story Coverage

- Loading states
- Empty states
- Error states
- Different match statuses
- Various data scenarios

## File Structure Summary

```
apps/web-med/src/
├── widgets/prospecting/                    # Prospecting widget system
├── components/tables/ListMatches.tsx      # Match table component (mode-aware)
├── hooks/lists/use-list-matches.ts        # Match list hook
├── www/core/matches/                       # Match list pages
├── www/core/match/                         # Match detail pages
├── www/core/provider-matches/              # Provider match pages (reuse components)
├── app/(app)/app/matches/                  # Organization route definitions
├── app/(app)/providers/app/matches/        # Provider route definitions
└── __stories__/                           # Storybook stories
    ├── components/tables/ListMatches.stories.tsx
    └── pages/core/matches/Matches.stories.tsx
```

## Optimized Implementation Approach

### Unified API Strategy

Instead of creating separate provider-specific endpoints, the system uses the core matches API (`api.jobs.matches`) with intelligent filtering based on user context.

**Key Benefits**:

- Single source of truth for match data
- Reduced code duplication
- Consistent behavior across organization and provider views
- Easier maintenance and testing

### Mode-Aware Components

The `ListMatches` component supports different modes via a `mode` prop:

- **Organization Mode**: Shows provider column, organization-focused actions
- **Provider Mode**: Shows organization column, provider-focused actions

**User Context Integration**: The system automatically detects user mode via `useUser().mode` (PROVIDER/ORGANIZATION) and filters data accordingly.

### Provider Navigation Updates

- **Replaced**: "Applications" and "Offers" navigation items
- **Added**: Single "Matches" navigation item
- **Route**: `/providers/app/matches` (uses same components with provider mode)

### Data Flow Optimization

**Unified Flow**:

```
Any Party → Create Match → Core Matches API → Mode-Aware UI → User-Specific View
```

This approach eliminates the need for separate provider-specific API endpoints while maintaining clear separation of concerns in the UI layer.

## Final Implementation Summary

### Unified Actions API

**Location**: `packages/api-medical/src/router/jobs/matches/actions.ts`

Consolidates all match actions (accept, decline, withdraw, apply) into a single router that handles both provider and organization perspectives:

- **Accept**: Providers accept org-initiated matches, orgs accept provider-initiated matches
- **Decline**: Either party can decline pending matches
- **Withdraw**: Only the initiating party can withdraw their own matches
- **Apply**: Providers can apply to jobs (creates provider-initiated matches)

### Mode-Aware Frontend

**Unified Components**:

- `ListMatches` component with `mode` prop (organization/provider)
- `Matches` page component that auto-detects user mode
- `MatchDetail` component with mode-specific UI and actions

**Smart Routing**:

- Organization routes: `/app/matches`, `/app/matches/[id]`
- Provider routes: `/providers/app/matches`, `/providers/app/matches/[id]`
- Same components, different modes and navigation

### Migration Benefits

1. **Reduced Code Duplication**: Single set of components for both user types
2. **Consistent Behavior**: Same logic for match management across all users
3. **Easier Maintenance**: One place to update match functionality
4. **Type Safety**: Unified API types and consistent data structures
5. **Better UX**: Consistent interface patterns for all users

## Provider-Side Requirements Analysis

### Current Provider System

The provider side currently uses separate applications and offers systems:

#### Applications System

- **Location**: `apps/web-med/src/www/providers/applications/`
- **Table**: `ListApplications` component
- **Actions**: Create, withdraw applications
- **Navigation**: `/providers/app/applications`

#### Offers System

- **Location**: `apps/web-med/src/www/providers/offers/`
- **Table**: `ListOffers` component
- **Actions**: Accept, reject offers
- **Navigation**: `/providers/app/offers`

### Migration to Matches

#### Required Changes

1. **Replace Applications/Offers with Matches**
   - Consolidate both systems into unified matches interface
   - Provider can view all matches (both initiated by them and received)
   - Single navigation item: "Matches" instead of "Applications" + "Offers"

2. **Provider Match Actions**
   - **Accept Match**: Provider accepts organization-initiated match
   - **Decline Match**: Provider declines any match
   - **Apply to Job**: Provider initiates match with organization
   - **Withdraw Match**: Provider withdraws their initiated match
   - **Negotiate Rate**: Provider proposes different rate

3. **Provider Navigation Updates**
   - Replace "Applications" and "Offers" with single "Matches" link
   - Update ProviderLayout navigation structure
   - Route: `/providers/app/matches`

#### API Requirements

New provider-side endpoints needed:

- `api.providers.matches.list` - List all matches for provider
- `api.providers.matches.accept` - Accept organization-initiated match
- `api.providers.matches.decline` - Decline any match
- `api.providers.matches.apply` - Create provider-initiated match
- `api.providers.matches.withdraw` - Withdraw provider-initiated match
- `api.providers.matches.negotiate` - Propose rate negotiation

#### Data Flow Changes

**Current Flow**:

```
Provider → Apply to Job → Application Created → Organization Reviews
Organization → Send Offer → Offer Created → Provider Reviews
```

**New Flow**:

```
Provider → Apply to Job → Match Created (PENDING) → Organization Reviews
Organization → Invite Provider → Match Created (PENDING) → Provider Reviews
Either Party → Accept/Decline → Match Status Updated
```
